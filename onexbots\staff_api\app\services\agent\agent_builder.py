from typing import Dict, List, Literal, Optional, Any

from langchain_core.tools import BaseTool
from langgraph.graph import (
    MessagesState,
)
from langgraph.graph.state import (
    CompiledGraph,
)
from langgraph.prebuilt import create_react_agent
from onexbots.shared.services.virtual_staff_service import get_virtual_staff
from onexbots.shared.services.virtual_staff_service import VirtualStaff
from onexbots.shared.services.knowledge_service import KnowledgeService
from onexbots.shared.services.embedding_service import EmbeddingService
from ..tools.registry import ToolManager
import logging
from langchain.tools.retriever import create_retriever_tool
from langchain_openai import ChatOpenAI
from langmem.short_term import SummarizationNode
from langchain_core.messages.utils import count_tokens_approximately
from async_lru import alru_cache
from onexbots.staff_api.app.services.prompts.registry import get_system_prompt
from langchain_core.prompts import ChatPromptTemplate
from onexbots.shared.config import settings
from langchain_core.messages import HumanMessage

# Configure logger
logger = logging.getLogger(__name__)

# Add this at the top-level, after logger definition
DEFAULT_TOOLS = ["timer"]

# Custom summary prompts to retain user-provided facts
INITIAL_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{messages}"),
        (
            "user",
            "Create a summary of the conversation above. IMPORTANT: Always retain any facts the user provides about themselves (e.g., name, preferences, requirements, etc.). Do NOT include any statements about the AI's inability to answer, lack of information, or apologies. Only summarize factual information and relevant context from the conversation.",
        ),
    ]
)

EXISTING_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{messages}"),
        (
            "user",
            "This is the summary so far: {existing_summary}\n\nExtend this summary by taking into account the new messages above. IMPORTANT: Do not lose any user-provided facts (such as name, preferences, requirements, etc.). Do NOT include any statements about the AI's inability to answer, lack of information, or apologies. Only summarize factual information and relevant context from the conversation.",
        ),
    ]
)

FINAL_SUMMARY_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("placeholder", "{system_message}"),
        ("system", "Summary of the conversation so far: {summary}"),
        ("placeholder", "{messages}"),
    ]
)


def should_continue(state: MessagesState) -> Literal["tools", "__end__"]:
    """Determine whether to continue calling tools or end the process."""
    messages = state["messages"]
    if not messages:
        return "__end__"  # End if no messages (edge case)
    last_message = messages[-1]
    # If the LLM called a tool, continue to the tools node
    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        return "tools"
    # Otherwise, end the cycle
    return "__end__"


def get_tools_for_staff(staff: VirtualStaff) -> List[BaseTool]:
    """Get tools based on staff configuration."""
    tools_list: List[BaseTool] = []
    tool_manager = ToolManager()
    # Get enabled tools from staff configuration
    enabled_tools = staff["configuration"].get("tools", {}).get("enabled", [])
    if enabled_tools is None:
        enabled_tools = []
    tool_configs = staff["configuration"].get("tools", {}).get("config", {})
    # Always include default tools
    enabled_tools = list(set(enabled_tools + DEFAULT_TOOLS))
    if not enabled_tools:
        return []
    for tool_name in enabled_tools:
        try:
            # Get tool configuration
            tool_config = None
            if tool_configs:
                tool_config = tool_configs.get(tool_name, {})
            # Get tool instance
            tool = tool_manager.get_tool(tool_name)
            if tool:
                # Configure tool if needed
                if tool_config:
                    # Update tool configuration
                    for key, value in tool_config.items():
                        if hasattr(tool, key):
                            setattr(tool, key, value)

                tools_list.append(tool)
                logger.debug(f"Added tool: {tool_name}")
            else:
                logger.warning(f"Tool not found: {tool_name}")

        except Exception as e:
            logger.error(f"Error loading tool {tool_name}: {str(e)}")

    logger.info(f"Loaded {len(tools_list)} tools for staff {staff['name']}")
    return tools_list


async def test_add_or_update_knowledge(knowledge_id: str):
    """Test adding or updating knowledge document."""
    embedding_service = EmbeddingService(settings)
    knowledge_service = KnowledgeService()
    knowledge = knowledge_service.get(knowledge_id)
    content = """
    [bot_direction]:[Đây là kịch bản chăm sóc khách hàng của VResort với câu hỏi của khách hàng và câu trả lời của AI, bạn phải trả lời theo kịch bản này  giống 100% (trong kịch bản, bạn đại diện cho AI, human đại diện cho khách hàng). Chỉ cần trả lời giống câu trả lời trong kịch bản, không cần phải thêm bớt từ ngữ]
HỎI ĐÁP VỀ VRESORT
-	Human: Tôi có thể đặt phòng bằng cách nào?
-   AI: Quý khách có 3 cách để đặt phòng tại VResort:
1. Gọi đến số điện thoại Khách sạn hoặc Hotline: 0832403636 / 0837403636
2. Đặt phòng trực tuyến tại: www.vresort.com.vn. Sau khi đặt, Khách sạn sẽ liên hệ lại.
3. Đặt qua các đại lý du lịch uy tín.
-	Human: Các loại phòng nào đang có sẵn tại Khách sạn?
-   AI: VResort hiện có các loại phòng ở các khu vực như:
- Khách sạn Sư Tử
- Villa Tiêu chuẩn
- Villa Cao cấp
- Nhà sàn
-	Human: Giá phòng tại khách sạn áp dụng theo phòng hay theo số lượng người?
-   AI: - Phòng khách sạn: tính theo tiêu chuẩn phòng và số người.
- Nhà sàn: tính theo số lượng người thực tế và mức tối thiểu mỗi nhà sàn.
-	Human: Tôi có phải thanh toán hay đặt cọc trước không?
-   AI: - Đặt trực tiếp: đặt cọc 50%, phần còn lại thanh toán tại lễ tân.
- Qua đại lý: thanh toán 100% giá phòng.
-	Human: Giá phòng bao gồm những gì?
-   AI: Bao gồm: bể bơi khoáng nóng trong nhà, bể khoáng ngoài trời, xông hơi khô, trò chơi miễn phí, tham quan, bữa sáng.
-	Human: Ăn sáng có bao gồm trong giá phòng không?
-   AI: Có. Trên 70 khách: buffet sáng. Dưới 70 khách: gọi món.
-	Human: Trong khu nghỉ có bể bơi nước nóng không?
-   AI: Có. 2 bể khoáng (nóng trong nhà và ngoài trời), miễn phí từ 7h00 – 21h00.
-	Human: Khách sạn Sư Tử có những hạng phòng nào?
-   AI: Tầng 2: 13 phòng 2 người (hướng núi, hướng bể bơi), 2 phòng Deluxe 4 người.
Tầng 3: 8 phòng 2 người, 4 phòng Deluxe 4 người.
-	Human: Bên bạn có villa 2-3-4 phòng ngủ không?
-   AI: Có 1 villa 4 phòng, 1 villa 5 phòng, nhiều villa tiêu chuẩn 6 phòng, và 4 villa cao cấp dạng bungalow.
-	Human: Villa tiêu chuẩn là villa như thế nào?
-   AI: Nhà riêng biệt 2 tầng, gồm 6 phòng (4 phòng 2 người, 2 phòng 4 người), không có bếp, khách, sinh hoạt chung.
Giá nguyên căn: 7.740k
Giá phòng 2 người: 1.090k
Giá phòng 4 người: 1.690k
-	Human: Villa Cao Cấp Hoa Mai như thế nào?
-   AI: Gồm 6 phòng, mỗi tầng 3 phòng. Có phòng khách. Không có bếp, sinh hoạt chung.
Giá nguyên căn: 13.140k
Phòng 2 người: 1.790k
Phòng 4 người: 2.990k
-	Human: Nhà sàn sẽ ở như thế nào?
-   AI: 1. Nằm trên sàn chung, không chia phòng.
2. Đệm lớn 2x3m, ở được 4-5 người/đệm.
3. Vệ sinh ở tầng 1.
4. Nếu đặt 1 phần: có vách ngăn, lối đi và chìa khóa riêng, dùng chung vệ sinh.
-	Human: Giá dịch vụ gala bao gồm những gì?
-   AI: - Âm thanh, ánh sáng, màn chiếu, máy chiếu
- Đầu karaoke
- Nhân viên kỹ thuật
- Tối đa 4 tiếng, không quá 23h00
-	Human: Trong khu nghỉ có phục vụ ăn trưa, tối không?
-   AI: Có. Nhà hàng phục vụ ăn sáng, trưa, tối. Thực đơn set hoặc gọi món.
-	Human: Tôi có thể hủy đặt phòng được không?
-   AI: Tùy điều kiện đặt phòng. Vui lòng liên hệ bộ phận đặt phòng để biết thêm chi tiết.
-	Human: Khách sạn có cho phép mang thú nuôi vào không?
-   AI: Không. VResort hiện không cho phép mang thú nuôi vào khu nghỉ.

"""
    metadata = {"type": "text", "knowledge_id": knowledge_id, "source": "DIRECT_TEXT"}

    try:
        # Test adding new knowledge document
        logger.info("Testing add new knowledge document...")
        embedding_knowledge = await embedding_service.add_or_update_knowledge_document(
            knowledge_id=knowledge_id,
            content=content,
            metadata=metadata,
            source="DIRECT_TEXT",
        )
        return embedding_knowledge
    except Exception as e:
        logger.error(f"Error during test: {str(e)}")
        raise


async def get_knowledge_tools(staff: VirtualStaff) -> List[dict]:
    """Get knowledge tools based on staff configuration."""
    knowledge_tools: List[dict] = []
    logger.info(staff.get("configuration").get("knowledge_base").get("knowledge_list"))
    # Check if knowledge base is enabled
    if not staff["configuration"].get("knowledge_base", {}).get("enabled"):
        logger.info(f"Knowledge base disabled for staff {staff['name']}")
        return knowledge_tools
    # Initialize knowledge service
    knowledge_service = KnowledgeService()
    # Get knowledge IDs from configuration
    knowledge_ids = (
        staff["configuration"].get("knowledge_base", {}).get("knowledge_ids", [])
    )
    if not knowledge_ids:
        return []

    for knowledge_id in knowledge_ids:
        # Get retriever for this knowledge
        await test_add_or_update_knowledge(knowledge_id)
        retriever = await knowledge_service.retriever(knowledge_id)
        if retriever:
            try:
                # Get the first document to extract summary using ainvoke
                docs = await retriever.ainvoke("")
                direction = docs[0].metadata.get("direction") if docs else None
                special_instruction = (
                    docs[0].metadata.get("special_instruction") if docs else None
                )

                # Use summary from retriever if available, otherwise use a default description
                description = direction or f"Knowledge base for {knowledge_id}"

                knowledge_tool = create_retriever_tool(
                    name=f"knowledge_{knowledge_id.replace('-', '_')}",
                    retriever=retriever,
                    description=description,
                )
                knowledge_tools.append(
                    {
                        "special_instruction": {
                            "name": f"knowledge_{knowledge_id.replace('-', '_')}",
                            "instruction": special_instruction,
                        },
                        "knowledge_tool": knowledge_tool,
                    }
                )
                logger.debug(f"Added knowledge tool for ID: {knowledge_id}")
            except Exception as e:
                logger.error(
                    f"Error creating knowledge tool for ID {knowledge_id}: {str(e)}"
                )
                continue
        else:
            logger.warning(
                f"Could not create retriever for knowledge ID: {knowledge_id}"
            )

    logger.info(
        f"Loaded {len(knowledge_tools)} knowledge tools for staff {staff['name']}"
    )
    return knowledge_tools


class State(MessagesState):
    context: dict[str, Any]
    remaining_steps: int = 5


# @alru_cache(maxsize=256, ttl=900)
async def build_dynamic_react_agent(staff_id: str) -> CompiledGraph:
    staff_data = get_virtual_staff(staff_id)
    staff = staff_data.get("data")
    logger.info("Build dynamic agent function called")
    knowledge_tools = await get_knowledge_tools(staff)
    skill_tools = get_tools_for_staff(staff)
    all_tools = skill_tools + [kt["knowledge_tool"] for kt in knowledge_tools]

    # Extract special instructions for knowledge tools
    knowledge_special_instructions = [
        kt["special_instruction"]
        for kt in knowledge_tools
        if kt.get("special_instruction")
    ]

    system_prompt = get_system_prompt(
        staff,
        metadata={"knowledge_special_instructions": knowledge_special_instructions},
    )

    # Get model name from configuration
    model_name = (
        staff["configuration"]
        .get("llm_settings", {})
        .get("llm_model", {})
        .get("model_name", "gpt-4o-mini")
    )

    model = ChatOpenAI(model="gpt-4o-mini")

    summarization_node = SummarizationNode(
        token_counter=count_tokens_approximately,
        model=model,
        max_tokens=8000,
        max_summary_tokens=700,
        # max_tokens=1000,
        # max_summary_tokens=700,
        output_messages_key="llm_input_messages",
        initial_summary_prompt=INITIAL_SUMMARY_PROMPT,
        existing_summary_prompt=EXISTING_SUMMARY_PROMPT,
        final_prompt=FINAL_SUMMARY_PROMPT,
    )

    def custom_pre_model_hook(state):
        messages = state["messages"]
        context = state.get("context", {})
        if not messages:
            return state
        last_human_idx = None

        # get last human message
        for i in range(len(messages) - 1, -1, -1):
            if isinstance(messages[i], HumanMessage):
                last_human_idx = i
                break

        if last_human_idx is None:
            summary = summarization_node.invoke(state)
            context = summary.get("context")
            return {**state, "context": context}

        # Only include context in summary_state if it is not None and not empty
        if context:
            summary_state = {"messages": messages[:last_human_idx], "context": context}
        else:
            summary_state = {"messages": messages[:last_human_idx]}
        summary = summarization_node.invoke(summary_state)
        context = summary.get("context")
        remaining_messages = messages[last_human_idx:]
        new_messages = summary.get("llm_input_messages", []) + remaining_messages
        # Only include context if it is not None and not empty
        new_state = {**state, "llm_input_messages": new_messages}
        if context:
            new_state["context"] = context
        elif "context" in new_state:
            del new_state["context"]
        return new_state

    # Create and return the ReAct agent with summarization node
    agent = create_react_agent(
        model=model_name,
        tools=all_tools,
        prompt=system_prompt,
        state_schema=State,
        pre_model_hook=custom_pre_model_hook,
    )
    return agent
