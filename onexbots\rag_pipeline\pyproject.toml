[project]
name = "onexbots-rag-pipeline"
version = "1.0.0"
description = "RAG Pipeline service for document processing and embedding generation"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "pydantic>=2.4.2",
    "pydantic-settings>=2.0.3",
    "python-multipart>=0.0.6",
    "langchain>=0.0.350",
    "langchain-openai>=0.0.2",
    "langchain-postgres>=0.0.1",
    "openai>=1.3.0",
    "psycopg2-binary>=2.9.9",
    "asyncpg>=0.29.0",
    "PyPDF2>=3.0.1",
    "python-docx>=1.0.0",
    "docx2txt>=0.8",
    "python-dotenv>=1.0.0",
    "boto3>=1.28.0",
    "prometheus-client>=0.19.0",
    "beautifulsoup4>=4.12.0",
    "requests>=2.31.0",
    "httpx>=0.25.2",
    "aiohttp>=3.9.0",
    "python-magic>=0.4.27",
    "tiktoken>=0.5.1",
    "sentence-transformers>=2.2.2",
    "faiss-cpu>=1.7.4",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "scikit-learn>=1.3.0",
    "unstructured[all-docs]>=0.10.0",
    "pdfminer.six>=20221105",
    "markdown>=3.4.0",
    "pillow>=10.0.0",
    "pdf2image>=1.16.3",
    "pytesseract>=0.3.10",
    "langchain-community>=0.0.10"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "isort>=5.0.0",
    "flake8>=4.0.0",
    "mypy>=0.910",
    "pre-commit>=3.0.0",
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0",
    "mkdocstrings>=0.23.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ["py39"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=app --cov-report=term-missing"

[tool.coverage.run]
source = ["app"]
omit = ["tests/*", "app/__init__.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
]

[tool.mkdocs]
site_name = "RAG Pipeline Documentation"
site_url = "https://your-org.github.io/onexbots/rag-pipeline"
site_dir = "site"
docs_dir = "docs"

[tool.mkdocs.theme]
name = "material"
palette = {primary = "blue", accent = "light blue"}
font = {text = "Roboto", code = "Roboto Mono"}

[tool.mkdocstrings]
handlers = {python = {rendering = {show_source = true}}}

[tool.hatch.build.targets.wheel]
packages = ["app"] 