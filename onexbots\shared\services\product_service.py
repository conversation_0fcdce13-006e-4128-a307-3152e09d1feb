import logging
from typing import Dict, Any, Optional, List
from onexbots.shared.config import settings
from .base_api_service import BaseAPIService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProductService(BaseAPIService[Dict[str, Any]]):
    """Service for managing products and interacting with the Optiwarehouse API."""

    def __init__(self, company_id: Optional[str] = None):
        super().__init__(
            api_url=settings.OPTIWAREHOUSE_SERVICES_API_URL,
        )
        self.company_id = company_id

    def list(self, **query_params) -> Optional[Dict[str, Any]]:
        """List all products.

        Args:
            **query_params: Query parameters for filtering and pagination
                - page: Page number (default: 1)
                - limit: Items per page (default: 10)
                - query: Search term
                - sort_: Sort field

        Returns:
            Optional[Dict[str, Any]]: Response containing products and pagination info
        """
        try:
            # Set default query parameters if none provided
            params = query_params or {"page": 1, "limit": 10}

            # Make the API request with query parameters
            response = self._make_request(
                method="GET",
                endpoint="/products",
                params=params,
                company_id=self.company_id,
                service="product"
            )

            # Log the response for debugging
            logger.debug(f"Product list response: {response}")

            return response
        except Exception as e:
            logger.error(f"Error listing products: {str(e)}")
            return None

    def create(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new product."""
        pass

    def get(self, id: str) -> Optional[Dict[str, Any]]:
        """Get a product by ID."""
        pass

    def delete(self, id: str) -> bool:
        """Delete a product by ID."""
        pass
