# ONEXBOTS Microservices Project

A microservices-based AI assistant platform built with FastAPI, LangChain, and PostgreSQL.

## Project Structure

```
onexbots/
├── staff_api/          # Staff API microservice
│   ├── app/           # FastAPI application code
│   ├── tests/         # Test files
│   ├── Dockerfile     # Docker configuration
│   └── pyproject.toml # Project dependencies and configuration
├── rag_pipeline/      # RAG pipeline service
│   ├── app/          # Application code
│   ├── tests/        # Test files
│   └── Dockerfile    # Docker configuration
├── shared/           # Shared utilities and services
│   ├── database/     # Database models and connections
│   ├── schemas/      # Shared Pydantic models
│   └── utils/        # Common utilities
├── monitoring/       # Monitoring and metrics
│   ├── prometheus/   # Prometheus configuration
│   └── grafana/      # Grafana dashboards
├── docs/            # Documentation
├── docker-compose.yml # Docker Compose configuration
└── README.md        # Project documentation
```

## Technology Stack

- **Programming Language**: Python
- **Web Framework**: FastAPI
- **AI Library**: LangChain
- **Database**: PostgreSQL
- **Message Processing**: AWS SQS with S3 events
- **Monitoring**: Prometheus & Grafana
- **Configuration**: Python-dotenv
- **Deployment**: Docker Compose
- **Package Management**: uv

## Getting Started

1. Clone the repository
2. Install uv package manager:
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```
3. Set up environment variables (see `.env.example`)
4. Create and activate a virtual environment:
   ```bash
   uv venv
   source .venv/bin/activate  # On Unix/macOS
   # or
   .venv\Scripts\activate  # On Windows
   ```
5. Install dependencies:
   ```bash
   uv pip install -r requirements.txt
   ```

## Docker Setup

### Building Docker Images

1. Build the staff_api image:
   ```bash
   cd onexbots
   docker build -t onexbots/staff_api -f staff_api/Dockerfile .
   ```

2. Build the rag_pipeline image:
   ```bash
   cd onexbots
   docker build -t onexbots/rag_pipeline -f rag_pipeline/Dockerfile .
   ```

### Running with Docker Compose

1. Start all services:
   ```bash
   cd onexbots
   docker-compose up
   ```

2. Start specific services:
   ```bash
   # Start only staff_api and its dependencies
   docker-compose up staff_api postgres

   # Start only rag_pipeline and its dependencies
   docker-compose up rag_pipeline postgres
   ```

3. Run in detached mode:
   ```bash
   docker-compose up -d
   ```

4. View logs:
   ```bash
   docker-compose logs -f [service_name]
   ```

5. Stop services:
   ```bash
   docker-compose down
   ```

## Development

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- PostgreSQL
- AWS CLI (for SQS/S3 integration)
- uv package manager

### Environment Setup

#### Required Environment Variables

Create a `.env` file in the root directory with the following variables:

```bash
# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=ONEXBOTS
DEBUG=True
ENVIRONMENT=development

# CORS Settings
CORS_ORIGINS=["*"]

# Database Settings
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=onexbots

# ONEXSTAFFS API Settings
ONEXSTAFFS_API_URL=http://localhost:8001
ONEXSTAFFS_API_KEY=your_api_key_here

# AWS Settings
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1

# Service-specific Settings
STAFF_API_HOST=0.0.0.0
STAFF_API_PORT=8000

RAG_API_HOST=0.0.0.0
RAG_API_PORT=8002

CHAT_API_HOST=0.0.0.0
CHAT_API_PORT=8003
```

#### Development Setup

1. Create and activate virtual environment:
   ```bash
   uv venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

2. Install development dependencies:
   ```bash
   uv pip install -r requirements-dev.txt
   ```

3. Set up pre-commit hooks:
   ```bash
   pre-commit install
   ```

#### Development Tools

The project includes several development tools:

- **Testing**: pytest, pytest-asyncio, pytest-cov
- **Code Quality**: black, isort, flake8, mypy
- **Documentation**: mkdocs, mkdocs-material
- **Development**: ipython, jupyter, pre-commit

### Code Quality

The project uses several tools to maintain code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Static type checking
- **pre-commit**: Git hooks for code quality

These tools are configured in:
- `pyproject.toml`: Tool configurations
- `.pre-commit-config.yaml`: Pre-commit hooks

### Running Locally

```bash
# Start all services
docker-compose up

# Start specific service
docker-compose up staff_api
docker-compose up rag_pipeline
```

## Documentation

- API Documentation: [Swagger UI](http://localhost:8000/docs)
- Architecture: See `docs/architecture.md`
- Deployment Guide: See `docs/deployment.md`

## Contributing

Please read `CONTRIBUTING.md` for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the `LICENSE` file for details.
