FROM python:3.11

WORKDIR /app

# Install system dependencies for document loaders
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        poppler-utils \
        tesseract-ocr \
        libtesseract-dev \
        libjpeg-dev \
        zlib1g-dev \
    && rm -rf /var/lib/apt/lists/*

# Create the onexbots directory structure
RUN mkdir -p /app/onexbots

# Copy the required files and directories
COPY __init__.py /app/onexbots/
COPY rag_pipeline /app/onexbots/rag_pipeline
COPY shared /app/onexbots/shared

# Copy pyproject.toml and install dependencies
WORKDIR /app/onexbots/rag_pipeline
RUN pip install --no-cache-dir .

# Set PYTHONPATH to include the project root
ENV PYTHONPATH=/app

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
