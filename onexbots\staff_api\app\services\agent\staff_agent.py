from typing import Dict, Any, List, Optional, AsyncGenerator
import logging
import asyncio
from uuid import uuid4
from datetime import datetime, timezone

from onexbots.shared.config import settings
from ..agent.base import BaseAgent
from onexbots.shared.services.knowledge_service import KnowledgeService
from onexbots.shared.services.conversation_service import MessageRole, Message
from ..mcp.client import <PERSON><PERSON><PERSON>
from ..mcp.config import MCPConfig
from onexbots.shared import ConversationService
from langgraph.types import RunnableConfig, StateSnapshot
from onexbots.shared.services.virtual_staff_service import VirtualStaff
from onexbots.shared.services.conversation_service import CreateConversationRequest
from app.utils.check_message_resolution import check_message_resolution

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StaffAgent(BaseAgent):
    """Staff-specific agent implementation."""

    def __init__(self, staff: VirtualStaff):
        """
        Initialize StaffAgent with staff configuration.

        Args:
            staff_config: Complete staff configuration dictionary including metadata and configuration
        """
        self.staff = staff
        self.conversation_service = ConversationService()

    @classmethod
    def _validate_staff_config(self) -> None:
        """Validate staff-specific configuration."""
        required_fields = ["name", "role", "department_id", "configuration"]
        for field in required_fields:
            if field not in self.staff_config:
                raise ValueError(f"Staff configuration missing required field: {field}")

        required_config_fields = ["personality", "knowledge_base"]
        for field in required_config_fields:
            if field not in self.staff_config["configuration"]:
                raise ValueError(
                    f"Staff configuration missing required field: configuration.{field}"
                )

    async def process_chat(
        self,
        message: str,
        staff_id: str,
        user_id: Optional[str] = None,
        phone_number: Optional[str] = None,
        name: Optional[str] = None,
        connection_id: Optional[str] = None,
        external_user_id: Optional[str] = None,
        channel_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Process a chat message and generate response.

        Args:
            message: The message to process
            staff_id: The staff member ID
            user_id: Optional user ID
            phone_number: Optional phone number
            name: Optional name
            connection_id: Optional connection ID
            external_user_id: Optional external user ID

        Returns:
            Dict containing conversation_id and response
        """
        try:
            user_created_at = datetime.now(timezone.utc).isoformat()
            bot_message_id = str(uuid4())
            # Create conversation with required information using CreateConversationRequest
            conversation_req = CreateConversationRequest(
                assignee_id=staff_id,
                staff_id=staff_id,
                role=MessageRole.VIRTUAL_STAFF.value,
                customer_name=name or "Anonymous",
                customer_phone_number=phone_number or external_user_id or "0000000000",
                user_id=user_id,
                connection_id=connection_id,
                channel_id=channel_id,
                external_user_id=external_user_id,
            )
            conversation_response = self.conversation_service.create_conversation(
                conversation_req
            )
            if not conversation_response or not conversation_response.get("success"):
                raise ValueError(
                    f"Failed to create conversation: {conversation_response.get('message') if conversation_response else 'Unknown error'}"
                )
            conversation = conversation_response["data"]
            conversation_id = conversation["id"]

            # Process message using configuration
            before_handle_time = datetime.now(timezone.utc)
            ai_response = await self.get_response(
                staff=self.staff, conversation_id=conversation_id, message=message
            )
            after_handle_time = datetime.now(timezone.utc)
            external_user_id = (
                external_user_id
                if external_user_id is not None
                else conversation.get("customer_id")
            )
            # Create AI response message data
            ai_message = Message(
                id=bot_message_id,
                content=ai_response,
                role=MessageRole.VIRTUAL_STAFF,
                connection_id=connection_id if connection_id else "",
                external_user_id=external_user_id,
                staff_id=staff_id,
                resolved_time=(after_handle_time - before_handle_time).total_seconds(),
            )

            user_message = Message(
                content=message,
                role=(
                    MessageRole.USER
                    if user_id is not None
                    else MessageRole.EXTERNAL_USER
                ),
                connection_id=connection_id,
                **({"user_id": user_id} if user_id is not None else {}),
                external_user_id=external_user_id,
                staff_id=staff_id,
                created_at=user_created_at,
                is_resolved=check_message_resolution(ai_response),
                response_message_id=bot_message_id,
            )

            # Log user message
            await self.conversation_service._log_conversation(
                conversation_id=conversation_id, message=user_message, response=None
            )

            # Log AI response
            await self.conversation_service._log_conversation(
                conversation_id=conversation_id, message=ai_message, response=None
            )

            return {
                "conversation_id": str(conversation_id),
                "response": ai_response,
                "conversation": conversation,
            }

        except Exception as e:
            logger.error(f"Error processing chat: {str(e)}")
            raise

    async def process_chat_stream(
        self,
        message: str,
        staff_id: str,
        user_id: Optional[str] = None,
        phone_number: Optional[str] = None,
        name: Optional[str] = None,
        connection_id: Optional[str] = None,
        external_user_id: Optional[str] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a chat message and stream the response.

        Args:
            message: The message to process
            staff_id: The staff member ID
            user_id: Optional user ID
            phone_number: Optional phone number
            name: Optional name
            connection_id: Optional connection ID
            external_user_id: Optional external user ID

        Yields:
            Dict containing content, conversation_id, and done flag
        """
        try:
            user_created_at = datetime.now(timezone.utc).isoformat()
            conversation_req = CreateConversationRequest(
                assignee_id=staff_id,
                staff_id=staff_id,
                role=MessageRole.VIRTUAL_STAFF.value,
                customer_name=name or "Anonymous",
                customer_phone_number=phone_number or external_user_id or "0000000000",
                user_id=user_id,
                connection_id=connection_id,
                external_user_id=external_user_id,
            )
            conversation_response = self.conversation_service.create_conversation(
                conversation_req
            )
            if not conversation_response or not conversation_response.get("success"):
                raise ValueError(
                    f"Failed to create conversation: {conversation_response.get('message') if conversation_response else 'Unknown error'}"
                )
            conversation = conversation_response["data"]
            conversation_id = conversation["id"]

            # Create user message data
            before_handle_time = datetime.now(timezone.utc)
            bot_message_id = str(uuid4())
            # Process message using configuration with streaming
            full_response = ""
            async for chunk in self.get_stream_response(
                staff=self.staff,
                message=message,
                conversation_id=conversation_id,
            ):
                full_response += chunk
                yield {
                    "content": chunk,
                    "conversation_id": str(conversation_id),
                    "done": False,
                }
            after_handle_time = datetime.now(timezone.utc)
            resolved_time = (after_handle_time - before_handle_time).total_seconds()
            final_message = Message(
                id=bot_message_id,
                content=full_response,
                staff_id=staff_id,
                role=MessageRole.VIRTUAL_STAFF,
                connection_id=connection_id if connection_id else "",
                resolved_time=resolved_time,
            )

            user_message = Message(
                content=message,
                role=MessageRole.EXTERNAL_USER,
                user_id=user_id,
                external_user_id=external_user_id,
                connection_id=connection_id,
                staff_id=staff_id,
                is_resolved=check_message_resolution(full_response),
                response_message_id=bot_message_id,
                created_at=user_created_at,
            )

            # Log user message
            await self.conversation_service._log_conversation(
                conversation_id=conversation_id, message=user_message, response=None
            )
            await self.conversation_service._log_conversation(
                conversation_id=str(conversation_id),
                message=final_message,
            )

            final_response_data = {
                "content": "",
                "conversation_id": str(conversation_id),
                "done": True,
            }
            yield final_response_data
        except Exception as e:
            logger.error(f"Error processing streaming chat: {str(e)}")
            yield {"content": f"Error: {str(e)}", "conversation_id": None, "done": True}

    async def process_chat_async(
        self,
        message: str,
        staff_id: str,
        external_user_id: Optional[str] = None,
        phone_number: Optional[str] = None,
        name: Optional[str] = None,
        connection_id: Optional[str] = None,
        channel_id: Optional[str] = None,
    ) -> None:
        """
        Schedule asynchronous message processing (similar to AWS Lambda invoke).
        Returns immediately while processing continues in the background.
        """
        try:
            asyncio.create_task(
                self.process_chat(
                    message=message,
                    staff_id=staff_id,
                    external_user_id=external_user_id,
                    phone_number=phone_number,
                    name=name,
                    connection_id=connection_id,
                    channel_id=channel_id,
                )
            )
        except Exception as e:
            logger.error(f"Error in process_chat_async: {str(e)}")
            raise
