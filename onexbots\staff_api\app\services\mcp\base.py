from typing import Dict, Any, Optional, List, Type, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from langchain_core.messages import BaseMessage
from langchain_core.runnables import RunnableConfig

class MCPAction(str, Enum):
    """Actions that can be taken by MCP rules."""
    BLOCK = "block"
    DELAY = "delay"
    MODIFY = "modify"

@dataclass
class MCPRule:
    """Configuration for MCP rules."""
    type: str
    action: MCPAction
    patterns: Optional[List[str]] = None
    replacement: Optional[str] = None
    max_messages: Optional[int] = None
    time_window: Optional[int] = None

class BaseMCPAdapter(ABC):
    """Base class for Model Context Protocol adapters."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the adapter with configuration."""
        self.config = config
        self._validate_config()
        self._initialize_from_config()
    
    def _initialize_from_config(self) -> None:
        """Initialize adapter attributes from config."""
        # Common attributes
        self.action = MCPAction(self.config.get("action", MCPAction.BLOCK))
        
        # Content filter specific
        if hasattr(self, "_is_content_filter"):
            self.patterns = self.config.get("patterns", [])
            self.replacement = self.config.get("replacement", "***")
        
        # Rate limiter specific
        if hasattr(self, "_is_rate_limiter"):
            self.max_messages = self.config.get("max_messages", 10)
            self.time_window = self.config.get("time_window", 60)
            self.user_history = {}
    
    @abstractmethod
    def _validate_config(self) -> None:
        """Validate adapter configuration."""
        if not isinstance(self.config, dict):
            raise ValueError("Configuration must be a dictionary")
        
        if "enabled" not in self.config:
            raise ValueError("Configuration missing 'enabled' field")
        
        if not isinstance(self.config["enabled"], bool):
            raise ValueError("'enabled' field must be a boolean")
    
    @abstractmethod
    async def process_messages(
        self,
        messages: List[Union[HumanMessage, AIMessage, SystemMessage]],
        user_id: str
    ) -> List[Union[HumanMessage, AIMessage, SystemMessage]]:
        """Process messages through the adapter."""
        if not self.config.get("enabled", False):
            return messages
        
        try:
            return await self._process_messages(messages, user_id)
        except Exception as e:
            # Log error but don't fail the request
            return messages
    
    async def _process_messages(
        self,
        messages: List[Union[HumanMessage, AIMessage, SystemMessage]],
        user_id: str
    ) -> List[Union[HumanMessage, AIMessage, SystemMessage]]:
        """Process messages through the adapter implementation."""
        raise NotImplementedError("Adapter must implement _process_messages method")
    
    def _handle_error(self, error: Exception) -> List[Union[HumanMessage, AIMessage, SystemMessage]]:
        """Handle errors gracefully."""
        return []
    
    def is_enabled(self) -> bool:
        """Check if the adapter is enabled in configuration."""
        return self.config.get("enabled", False)

    async def process(self, message: str, user_id: str) -> str:
        """Process a single message through the adapter."""
        try:
            # Convert string message to HumanMessage
            messages = [HumanMessage(content=message)]
            
            # Process messages
            processed_messages = await self.process_messages(messages, user_id)
            
            # If no messages returned, message was blocked
            if not processed_messages:
                return ""
            
            # Return content of first message
            return processed_messages[0].content
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return message 