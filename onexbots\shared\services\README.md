# Staff Configuration Guide

This document outlines the complete configuration structure for staff members in the system.

## Configuration Structure

```json
{
  "llm": {
    "provider": "openai",
    "model": "gpt-4-turbo-preview",
    "temperature": 0.7,
    "max_tokens": 2000,
    "api_key": "your-api-key"
  },
  "personality": {
    "name": "<PERSON>",
    "role": "Customer Support Specialist",
    "traits": [
      "Professional",
      "Friendly",
      "Patient",
      "Problem-solver"
    ],
    "greeting": "Hello! I'm <PERSON>, your customer support specialist. How can I help you today?",
    "farewell": "Thank you for chatting with me. Have a great day!",
    "tone": "professional but friendly",
    "language": "en-US"
  },
  "knowledge_base": {
    "enabled": true,
    "allowed_knowledge_ids": [
      "product-guide",
      "faq",
      "pricing",
      "support-policy"
    ],
    "max_context_tokens": 2000,
    "min_similarity_score": 0.7,
    "context_template": "Use the following context to answer the question:\n{context}\n\nQuestion: {question}"
  },
  "tools": {
    "enabled": [
      "calculator",
      "weather",
      "search"
    ],
    "config": {
      "calculator": {
        "enabled": true,
        "allowed_operations": ["add", "subtract", "multiply", "divide"],
        "max_expression_length": 100,
        "max_number": 1000000
      },
      "weather": {
        "enabled": true,
        "api_key": "your-weather-api-key",
        "allowed_locations": ["US", "CA", "UK"],
        "cache_duration": 3600
      },
      "search": {
        "enabled": true,
        "api_key": "your-search-api-key",
        "max_results": 5,
        "safe_search": true
      }
    }
  },
  "mcp_config": {
    "enabled": true,
    "rules": [
      {
        "type": "content_filter",
        "action": "block",
        "patterns": [
          "inappropriate_word_1",
          "inappropriate_word_2"
        ],
        "replacement": "***"
      },
      {
        "type": "rate_limiter",
        "action": "delay",
        "max_messages": 10,
        "time_window": 60
      },
      {
        "type": "text_modification",
        "action": "modify",
        "patterns": [
          {
            "pattern": "\\b(\\d{3})[-.]?(\\d{3})[-.]?(\\d{4})\\b",
            "replacement": "***-***-****"
          }
        ]
      }
    ]
  },
  "conversation": {
    "max_history_length": 10,
    "context_window": 5,
    "response_timeout": 30,
    "fallback_message": "I apologize, but I'm having trouble processing your request right now. Please try again in a moment."
  }
}
```

## Configuration Sections

### LLM Configuration
- `provider`: The LLM provider (e.g., "openai", "anthropic")
- `model`: The specific model to use
- `temperature`: Controls response randomness (0.0 to 1.0)
- `max_tokens`: Maximum response length
- `api_key`: Provider API key

### Personality Configuration
- `name`: Staff member's name
- `role`: Staff member's role
- `traits`: List of personality traits
- `greeting`: Initial greeting message
- `farewell`: Closing message
- `tone`: Communication tone
- `language`: Primary language

### Knowledge Base Configuration
- `enabled`: Whether knowledge base is active
- `allowed_knowledge_ids`: List of accessible knowledge documents
- `max_context_tokens`: Maximum context length
- `min_similarity_score`: Minimum relevance score
- `context_template`: Template for context usage

### Tools Configuration
- `enabled`: List of enabled tools
- `config`: Tool-specific configurations
  - Calculator: Mathematical operations
  - Weather: Weather information
  - Search: Web search capabilities

### MCP (Message Control Protocol) Configuration
- `enabled`: Whether MCP is active
- `rules`: List of message processing rules
  - Content Filter: Filters inappropriate content
  - Rate Limiter: Controls message frequency
  - Text Modification: Modifies sensitive information

### Conversation Configuration
- `max_history_length`: Maximum conversation history length
- `context_window`: Number of messages to consider for context
- `response_timeout`: Maximum response time in seconds
- `fallback_message`: Message to use when processing fails

## Usage Example

```python
from onexbots.shared.services import StaffService

# Initialize staff service
staff_service = StaffService()

# Create staff configuration
config = {
    "llm": {
        "provider": "openai",
        "model": "gpt-4-turbo-preview",
        "temperature": 0.7
    },
    "personality": {
        "name": "Alex",
        "role": "Customer Support"
    },
    # ... other configuration sections
}

# Create staff member
staff = await staff_service.create_staff(config)

# Process message
response = await staff_service.process_message(
    staff_id=staff["id"],
    message="Hello, how can I help you?",
    user_id="user123"
) 