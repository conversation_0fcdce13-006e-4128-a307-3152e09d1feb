import boto3
from typing import Optional
from pydantic import BaseModel
from ..utils.file_handlers import (
    <PERSON><PERSON><PERSON><PERSON>,
    DocxHandler,
    TextHandler,
    URLHandler,
    HTMLHandler,
)
from pathlib import Path
import tempfile
import os
import logging
from app.core.config import rag_settings
from onexbots.shared.config import settings
from langchain_openai import ChatOpenAI
from langchain_community.document_loaders import (
    UnstructuredPDFLoader,
    TextLoader,
    Docx2txtLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader,
    WebBaseLoader,
)

logger = logging.getLogger(__name__)


class ProcessedFile(BaseModel):
    content: str
    metadata: dict
    file_type: str
    summary: Optional[str] = None


class FileProcessor:
    def __init__(self):
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION,
        )
        self.handlers = {
            "pdf": <PERSON><PERSON><PERSON><PERSON>(),
            "docx": <PERSON><PERSON><PERSON><PERSON><PERSON>(),
            "txt": <PERSON><PERSON><PERSON><PERSON>(),
            "url": URLHandler(),
            "html": HTMLHandler(),
        }
        self.llm = ChatOpenAI(
            model_name="gpt-3.5-turbo",
            temperature=0.7,
            streaming=False,  # Enable streaming by default
            max_tokens=2000,
            api_key=settings.OPENAI_API_KEY,
        )

    async def download_from_s3(self, bucket: str, key: str) -> Optional[Path]:
        """Download file from S3 to a temporary location."""
        try:
            # Create a temporary file
            temp_dir = tempfile.mkdtemp()
            file_path = Path(temp_dir) / Path(key).name

            # Download file from S3
            self.s3_client.download_file(bucket, key, str(file_path))
            logger.info(f"Successfully downloaded file from S3: {key}")

            return file_path
        except Exception as e:
            logger.error(f"Error downloading file from S3: {str(e)}")
            return None

    async def process_file(self, file_path: Path) -> Optional[dict]:
        """Process file based on its type and return content and metadata."""
        try:
            file_extension = file_path.suffix.lower()
            logger.info(f"Processing file: {file_path.name}")
            if file_extension not in rag_settings.supported_file_types_list:
                raise ValueError(f"Unsupported file type: {file_path}")
            if file_extension == ".url":
                with open(file_path, "r", encoding="utf-8") as f:
                    url = f.read().strip()
                loader = WebBaseLoader(web_path=url, verify_ssl=False)
            else:
                if file_extension == ".pdf":
                    loader = UnstructuredPDFLoader(file_path)
                elif file_extension in [".doc", ".docx"]:
                    loader = Docx2txtLoader(file_path)
                elif file_extension == ".txt":
                    loader = TextLoader(file_path, encoding="utf-8")
                elif file_extension == ".html":
                    loader = UnstructuredHTMLLoader(file_path)
                elif file_extension == ".md":
                    loader = UnstructuredMarkdownLoader(file_path)
                else:
                    raise ValueError(
                        f"This file type is currently not supported: {file_path}"
                    )

            documents = loader.load()
            contents = []
            for doc in documents:
                contents.append(doc.page_content)
            content = "\n".join(contents)
            file_extension = file_extension.replace(".", "")


            return {
                "content": content,
                "metadata": {
                    "type": file_extension,
                    "filename": file_path.name,
                },
            }
        except Exception as e:
            logger.error(f"Error processing file: {str(e)}")
            return None
        finally:
            # Clean up temporary file
            if file_path.exists():
                os.remove(file_path)
                os.rmdir(file_path.parent)
                logger.debug(f"Cleaned up temporary file: {file_path}")
