# 🏷️ Quy trình Tăng Version Tự Động với GitHub Actions

Dự án này sử dụng GitHub Actions để **tự động tạo Git tag** dựa trên nhánh và nội dung commit nhằm quản lý version theo chuẩn [Semantic Versioning](https://semver.org/).

---

## 🌱 Quy ước nhánh

- `dev`: <PERSON><PERSON><PERSON><PERSON>h<PERSON>t triển, mỗi lần merge vào đây sẽ tạo một tag dạng `vX.Y.Z-dev`
- `main`: Nhánh ổn định (release), mỗi lần merge vào đây sẽ tạo tag `vX.Y.Z` (không `-dev`)

---

## 📦 Quy tắc tăng version

Tăng version được xác định dựa trên commit message trong PR hoặc merge vào `dev`.

| Kiểu commit        | Kết quả                  | Tag dạng         |
|--------------------|--------------------------|------------------|
| `fix:`             | Tăng `patch`             | `vX.Y.Z-dev.N+1` |
| `feat:`            | Tăng `minor`, reset `patch` | `vX.Y+1.0-dev.N` |
| `BREAKING CHANGE`  | Tăng `major`, reset `minor`, `patch` | `vX+1.0.0-dev.N` |
| Khác               | Mặc định tăng `patch`    | `vX.Y.Z+1-dev.N` |

---

## 🧪 Ví dụ minh họa

### Lần đầu (không có tag):
- Push vào `dev` → tạo `v0.0.1-dev.1`

### Sau khi có tag `v1.2.3`:
- Merge PR với commit `feat: thêm chức năng A` → tạo `v1.3.0-dev.1`
- Merge PR với commit `fix: sửa lỗi B` → tạo `v1.3.0-dev.2`
- Merge PR có `BREAKING CHANGE` → tạo `v2.0.0-dev`

### Khi dev ổn định:
- Merge `dev` → `main`  
- CI/CD tạo tag mới: `v2.0.0` (release version)

---

## 🧰 Cấu trúc tag

| Nhánh    | Dạng tag       | Mô tả                  |
|----------|----------------|------------------------|
| `dev`    | `vX.Y.Z-dev.N` | Bản phát triển         |
| `main`   | `vX.Y.Z`       | Bản release chính thức |

---

## 🔧 Kỹ thuật triển khai

- GitHub Actions workflow tự động:
  - Phân tích commit trong phạm vi từ tag release gần nhất → `HEAD`
  - Xác định mức tăng version phù hợp
  - Gắn tag mới và push lên GitHub

---

## 📋 Lưu ý cho dev

- Hãy viết commit message theo chuẩn:
  - `feat: thêm tính năng`
  - `fix: sửa lỗi`
  - `chore!: đổi cấu trúc API\n\nBREAKING CHANGE: ...`

- Hoặc sử dụng PR labels nếu được hỗ trợ (ví dụ `bump:minor`, `bump:major`)

---

## 🔐 Tích hợp CI/CD

- Bạn có thể gắn release workflow vào `main`:
  - Publish artifact, Docker image, hoặc upload release lên GitHub
  - Chỉ chạy khi tag mới không có hậu tố `-dev`

---

## 📁 Tệp liên quan

- `.github/workflows/release.yml`: (tuỳ chọn) workflow release trên `main, dev`

---

## 🤝 Đóng góp

Đảm bảo commit đúng chuẩn để hệ thống tự động nhận diện đúng version.

```bash
git commit -m "feat: thêm đăng nhập OAuth"
git commit -m "fix: lỗi gọi API"
git commit -m "chore!: thay đổi lớn\n\nBREAKING CHANGE: loại bỏ field password"
