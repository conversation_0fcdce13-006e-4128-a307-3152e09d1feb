import logging
from typing import Dict, Any, List, Optional, Type
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from pydantic import BaseModel
from langchain_core.messages import BaseMessage
from langchain_core.runnables import RunnableConfig

from .base import BaseMCPAdapter, MCPAction
from .registry import MCPRegistry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MCPRequest(BaseModel):
    """Request model for MCP server."""
    messages: List[Dict[str, Any]]
    config: Optional[Dict[str, Any]] = None
    user_id: str

class MCPResponse(BaseModel):
    """Response model for MCP server."""
    messages: List[Dict[str, Any]]
    context: Dict[str, Any]

class MCPServer:
    """Model Context Protocol server for managing context and message processing."""
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.registry = MCPRegistry()
        self.context_store: Dict[str, Dict[str, Any]] = {}
        self._setup_routes()
    
    def _setup_routes(self):
        """Set up FastAPI routes for MCP server."""
        
        @self.app.post("/mcp/process")
        async def process_messages(request: MCPRequest) -> MCPResponse:
            """Process messages through MCP adapters."""
            try:
                # Convert messages to LangChain format
                messages = self._convert_to_langchain_messages(request.messages)
                
                # Get user context
                context = self.context_store.get(request.user_id, {})
                
                # Create config with context
                config = request.config or {}
                config["user_id"] = request.user_id
                config["context"] = context
                
                # Process messages through adapters
                processed_messages = await self._process_messages(messages, config)
                
                # Update context
                self._update_context(request.user_id, context)
                
                # Convert back to dict format
                response_messages = self._convert_from_langchain_messages(processed_messages)
                
                return MCPResponse(
                    messages=response_messages,
                    context=context
                )
                
            except Exception as e:
                logger.error(f"Error processing messages: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/mcp/context/{user_id}")
        async def get_context(user_id: str) -> Dict[str, Any]:
            """Get user context."""
            return self.context_store.get(user_id, {})
        
        @self.app.delete("/mcp/context/{user_id}")
        async def clear_context(user_id: str):
            """Clear user context."""
            if user_id in self.context_store:
                del self.context_store[user_id]
    
    def _convert_to_langchain_messages(self, messages: List[Dict[str, Any]]) -> List[BaseMessage]:
        """Convert dict messages to LangChain format."""
        from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
        
        converted = []
        for msg in messages:
            msg_type = msg.get("type", "human")
            content = msg.get("content", "")
            
            if msg_type == "human":
                converted.append(HumanMessage(content=content))
            elif msg_type == "ai":
                converted.append(AIMessage(content=content))
            elif msg_type == "system":
                converted.append(SystemMessage(content=content))
        
        return converted
    
    def _convert_from_langchain_messages(self, messages: List[BaseMessage]) -> List[Dict[str, Any]]:
        """Convert LangChain messages to dict format."""
        converted = []
        for msg in messages:
            msg_dict = {
                "type": msg.__class__.__name__.lower().replace("message", ""),
                "content": msg.content
            }
            converted.append(msg_dict)
        return converted
    
    async def _process_messages(
        self,
        messages: List[BaseMessage],
        config: Dict[str, Any]
    ) -> List[BaseMessage]:
        """Process messages through all enabled adapters."""
        processed_messages = messages
        
        # Get enabled adapters from config
        enabled_adapters = config.get("enabled_adapters", [])
        
        for adapter_type in enabled_adapters:
            adapter = self.registry.create_rule(adapter_type, config)
            if adapter:
                processed_messages = await adapter.process_messages(
                    processed_messages,
                    RunnableConfig(**config)
                )
        
        return processed_messages
    
    def _update_context(self, user_id: str, context: Dict[str, Any]):
        """Update user context."""
        self.context_store[user_id] = context
    
    def register_adapter(self, adapter_type: str, adapter_class: Type[BaseMCPAdapter]):
        """Register a new MCP adapter."""
        self.registry.register_rule(adapter_type, adapter_class) 