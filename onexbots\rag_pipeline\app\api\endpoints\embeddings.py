from fastapi import APIRouter, HTTPException
from typing import List, Optional

from onexbots.shared.services.embedding_service import EmbeddingService
from onexbots.shared.config import settings

router = APIRouter()
embedding_service = EmbeddingService(settings)

@router.post("/generate")
async def generate_embeddings(text: str, metadata: Optional[dict] = None):
    """
    Generate embeddings for the given text and metadata.
    
    Args:
        text: The text to generate embeddings for
        metadata: Optional metadata associated with the text
        
    Returns:
        dict: Generated embeddings and metadata
    """
    try:
        result = await embedding_service.generate_embeddings(text, metadata or {})
        if result is None:
            raise HTTPException(status_code=500, detail="Failed to generate embeddings")
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/search")
async def search_similar(query: str, limit: int = 5):
    """
    Search for similar embeddings.
    
    Args:
        query: The search query text
        limit: Maximum number of results to return (default: 5)
        
    Returns:
        List[dict]: List of similar documents with scores
    """
    try:
        results = await embedding_service.search_similar(query, limit=limit)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 