repos:
    - repo: https://github.com/commitizen-tools/commitizen
      rev: v3.20.0
      hooks:
          - id: commitizen
            stages: [commit-msg]

#    - repo: https://github.com/pre-commit/pre-commit-hooks
#      rev: v4.5.0
#      hooks:
#          - id: trailing-whitespace
#          - id: end-of-file-fixer
#          - id: check-yaml
#          - id: check-added-large-files
#          - id: check-ast
#          - id: check-json
#          - id: check-merge-conflict
#          - id: check-toml
#          - id: debug-statements
#          - id: detect-private-key
#          - id: mixed-line-ending
#          - id: name-tests-test
#          - id: requirements-txt-fixer
#
#    - repo: https://github.com/psf/black
#      rev: 23.11.0
#      hooks:
#          - id: black
#            language_version: python3.9
#
#    - repo: https://github.com/pycqa/isort
#      rev: 5.12.0
#      hooks:
#          - id: isort
#            args: ["--profile", "black"]
#
#    - repo: https://github.com/pycqa/flake8
#      rev: 6.1.0
#      hooks:
#          - id: flake8
#            additional_dependencies:
#                [
#                    "flake8-docstrings",
#                    "flake8-bugbear",
#                    "flake8-comprehensions",
#                    "flake8-simplify",
#                ]
#
#    - repo: https://github.com/pre-commit/mirrors-mypy
#      rev: v1.7.0
#      hooks:
#          - id: mypy
#            additional_dependencies:
#                [
#                    "types-requests",
#                    "types-python-dateutil",
#                    "types-pytz",
#                    "types-PyYAML",
#                    "types-setuptools",
#                ]
