module.exports = {
    branches: ["main", { name: "dev", channel: "dev", prerelease: "dev" }],
    tagFormat: "v${version}",
    plugins: [
        "@semantic-release/commit-analyzer",
        "@semantic-release/release-notes-generator",
        "@semantic-release/github",
        "@semantic-release/git",
        [
        "@semantic-release/exec",
          {
            "publishCmd": "echo ${nextRelease.version} > release-version.txt"
          }
        ]

    ],
    "dryRun": false
};
