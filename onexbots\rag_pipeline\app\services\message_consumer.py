import asyncio
import logging
import j<PERSON>
from typing import Dict, Any, Optional, List
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, EndpointConnectionError
from ..core.config import rag_settings
from onexbots.shared.config import settings
from onexbots.shared.services.knowledge_service import KnowledgeService
from .file_processor import FileProcessor

logger = logging.getLogger(__name__)

class MessageConsumer:
    def __init__(self):
        self.sqs = boto3.client(
            'sqs',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION
        )
        self.s3 = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION
        )
        self.queue_url = rag_settings.SQS_QUEUE_URL
        self.retriever_queue_url = rag_settings.SQS_RETRIEVER_QUEUE_URL
        self.dlq_queue_url = rag_settings.SQS_DLQ_QUEUE_URL
        self.dlq_retriever_queue_url = rag_settings.SQS_DLQ_RETRIEVER_QUEUE_URL
        self.file_processor = FileProcessor()
        self.knowledge_service = KnowledgeService()

    async def delete_message(self, message: Dict[str, Any], queue_url: str = None) -> bool:
        """Delete a message from the SQS queue after successful processing."""
        try:
            if queue_url is None:
                queue_url = self.queue_url
            self.sqs.delete_message(
                QueueUrl=queue_url,
                ReceiptHandle=message['ReceiptHandle']
            )
            return True
        except Exception as e:
            logger.error(f"Error deleting message from queue: {str(e)}")
            return False

    async def process_sqs_message(self, message: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Process a single message from the queue."""
        try:
            # Parse the message body
            record_data = json.loads(message['Body'])
            return record_data
            
        except json.JSONDecodeError:
            logger.error("Invalid message format: not valid JSON")
            return None
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return None
        
    async def process_message(self, message: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Process a single message from the queue."""
        try:
            # Parse the message body
            body = json.loads(message['Body'])
            
            # Extract S3 event details
            if 'Records' not in body or not body['Records']:
                logger.error("No S3 event records found in message")
                return None
                
            processed_records = []
            for record in body['Records']:
                if record.get('eventSource') != 'aws:s3':
                    logger.warning(f"Skipping non-S3 event: {record.get('eventSource')}")
                    continue
                    
                # Extract bucket and key
                bucket = record['s3']['bucket']['name']
                key = record['s3']['object']['key']
                
                # Extract company_id and knowledge_id from key
                # Expected format: knowledge/{company_id}/{knowledge_id}.txt
                try:
                    _, company_id, knowledge_file = key.split('/')
                    knowledge_id = knowledge_file.split('.')[0]
                except ValueError:
                    logger.error(f"Invalid key format: {key}")
                    continue
                    
                # Update knowledge status to processing
                result = self.knowledge_service.update_status(
                    id=knowledge_id,
                    status="PROCESSING",
                    company_id=company_id
                )
                
                if not result:
                    logger.error(f"Failed to update knowledge status for {key}")
                    continue
                
                processed_records.append({
                    "bucket": bucket,
                    "key": key,
                    "company_id": company_id,
                    "knowledge_id": knowledge_id
                })
            
            return processed_records if processed_records else None
            
        except json.JSONDecodeError:
            logger.error("Invalid message format: not valid JSON")
            return None
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return None

    async def consume_messages(self, queue_url: str = None) -> None:
        """Consume messages from the queue."""
        while True:
            try:
                if queue_url is None:
                    queue_url = self.queue_url
                response = self.sqs.receive_message(
                    QueueUrl=queue_url,
                    MaxNumberOfMessages=1,
                    WaitTimeSeconds=20
                )

                if 'Messages' in response:
                    for message in response['Messages']:
                        try:
                            await self.process_message(message)
                            # Delete the message from the queue
                            await self.delete_message(message)
                        except Exception as e:
                            logger.error(f"Error processing message: {str(e)}")
                            continue

            except ClientError as e:
                error_code = e.response['Error']['Code']
                if error_code == 'InvalidClientTokenId':
                    logger.error("Invalid AWS credentials. Please check your AWS configuration.")
                elif error_code == 'AccessDenied':
                    logger.error("Access denied to SQS queue. Please check your permissions.")
                else:
                    logger.error(f"SQS error: {str(e)}")
                await asyncio.sleep(60)  # Wait before retrying
            except NoCredentialsError:
                logger.error("AWS credentials not found. Please check your AWS configuration.")
                await asyncio.sleep(60)  # Wait before retrying
            except EndpointConnectionError:
                logger.error("Could not connect to SQS endpoint. Please check your network connection.")
                await asyncio.sleep(60)  # Wait before retrying
            except Exception as e:
                logger.error(f"Unexpected error in message consumption: {str(e)}")
                await asyncio.sleep(60)  # Wait before retrying 
                
    async def move_to_dlq(self, message: Dict[str, Any]) -> None:
        """Move a message to the DLQ."""
        self.sqs.send_message(
            QueueUrl=self.dlq_queue_url,
            MessageBody=message['Body'],
            MessageAttributes={
                'Error': {
                    'DataType': 'String',
                    'StringValue': 'Error processing message'
                }
            }
        )
        await self.delete_message(message, self.queue_url)
        
    async def move_to_retriever_dlq(self, message: Dict[str, Any]) -> None:
        """Move a message to the DLQ."""
        self.sqs.send_message(
            QueueUrl=self.dlq_retriever_queue_url,
            MessageBody=message['Body'],
            MessageAttributes={
                'Error': {
                    'DataType': 'String',
                    'StringValue': 'Error processing message'
                }
            }
        )
        await self.delete_message(message, self.retriever_queue_url)