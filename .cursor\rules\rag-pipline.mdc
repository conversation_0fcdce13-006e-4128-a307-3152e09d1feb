---
description: 
globs: 
alwaysApply: true
---
for ANY question about <PERSON><PERSON><PERSON><PERSON>, use the langgraph-docs-mcp server to help answer -- 
+ call list_doc_sources tool to get the available llms.txt file
+ call fetch_docs tool to read it
+ reflect on the urls in llms.txt 
+ reflect on the input question 
+ call fetch_docs on any urls relevant to the question

+ use this to answer the question