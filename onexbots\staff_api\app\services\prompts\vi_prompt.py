from .base_prompt import BaseSystemPrompt
from onexbots.shared.services.virtual_staff_service import (
    Tone,
    Language,
    Temper,
    ResponseLength,
    Role,
)


class VietnameseSystemPrompt(BaseSystemPrompt):
    def get_personality_settings(self, personality: dict) -> tuple[str, str]:
        """Get personality settings and communication language.

        Args:
            personality: Dictionary containing personality settings

        Returns:
            tuple: (personality_settings, communication_language)
        """
        personality_settings = ""
        communication_language = self.convert_language_code_to_name(
            personality.get("language", Language.VI)
        )

        if personality:
            if personality.get("tone"):
                tone = personality["tone"]
                tone_desc = {
                    Tone.FRIENDLY.value: "Thân thiện - Giao tiếp gần gũi, cởi mở, tạo cảm giác thoải mái cho người dùng",
                    Tone.PROFESSIONAL.value: "Chuyên nghiệp - Giao tiếp trang trọng, lịch s<PERSON>, tập trung vào hiệu quả công việc",
                    Tone.CHEERFUL.value: "Vui vẻ - <PERSON>ia<PERSON> tiế<PERSON> tích cự<PERSON>, l<PERSON><PERSON> quan, t<PERSON><PERSON> kh<PERSON>ng khí thoải mái",
                    <PERSON><PERSON>.ASSERTIVE.value: "Quyết đoán - Giao tiếp rõ ràng, tự tin, đi thẳng vào vấn đề",
                    Tone.HUMOROUS.value: "Hài hước - Giao tiếp vui vẻ, có thể sử dụng ngôn ngữ hài hước phù hợp",
                    Tone.EMPATHETIC.value: "Đồng cảm - Giao tiếp thấu hiểu, quan tâm đến cảm xúc của người dùng",
                }.get(tone, tone)
                personality_settings += f"\nGiọng điệu: {tone_desc}"

            if personality.get("language"):
                lang = personality["language"]
                communication_language = self.convert_language_code_to_name(lang)
                personality_settings += f"\nNgôn ngữ: {communication_language}"

            if personality.get("response_length"):
                length = personality["response_length"]
                length_desc = {
                    ResponseLength.SHORT.value: "Ngắn gọn - Trả lời ngắn, súc tích, tập trung vào thông tin chính",
                    ResponseLength.MEDIUM.value: "Vừa phải - Trả lời đầy đủ thông tin, cân bằng giữa ngắn gọn và chi tiết",
                    ResponseLength.LONG.value: "Chi tiết - Trả lời đầy đủ, giải thích kỹ lưỡng, bao gồm nhiều ví dụ và phân tích",
                }.get(length, length)
                personality_settings += f"\nĐộ dài phản hồi: {length_desc}"

            if personality.get("temp"):
                temp = personality["temp"]
                temp_desc = {
                    Temper.LOW.value: "Thấp - Phản hồi an toàn, ít sáng tạo, tập trung vào thông tin chính xác",
                    Temper.MEDIUM.value: "Trung bình - Cân bằng giữa sáng tạo và an toàn, cho phép một số biến thể trong câu trả lời",
                    Temper.HIGH.value: "Cao - Phản hồi sáng tạo, linh hoạt, có thể đưa ra nhiều cách tiếp cận khác nhau",
                }.get(temp, temp)
                personality_settings += f"\nNhiệt độ: {temp_desc}"

            if personality.get("farewell"):
                personality_settings += f"\nLời chào kết: {personality['farewell']}"

        return personality_settings, communication_language

    def build(self):
        staff = self.staff
        metadata = self.metadata
        # Lấy thông tin cơ bản của nhân viên
        staff_name = staff.get("name", "")
        staff_role = staff.get("role", "")
        staff_greeting = staff.get("greeting", "")
        staff_domain_expertise = staff.get("domain_expertise", [])

        # Lấy cấu hình
        config = staff.get("configuration", {})
        instruction = config.get("instruction", "")

        # Lấy thiết lập tính cách
        personality = config.get("personality", {})
        personality_settings, communication_language = self.get_personality_settings(
            personality
        )

        tool_scope_instruction = "Bạn chỉ được phép trả lời các câu hỏi nằm trong phạm vi kiến thức đã cung cấp và những thông tin trong ngữ cảnh hội thoại. Không trả lời các câu hỏi ngoài phạm vi này."

        role_desc = {
            Role.CUSTOMER_SUPPORT_AGENT.value: "Chuyên gia hỗ trợ khách hàng",
            Role.SALES_ASSISTANT.value: "Trợ lý kinh doanh",
            Role.VIRTUAL_PERSONAL_ASSISTANT.value: "Trợ lý cá nhân ảo",
            Role.TECHNICAL_SUPPORT_SPECIALIST.value: "Chuyên gia hỗ trợ kỹ thuật",
            Role.HR_RECRUITMENT_ASSISTANT.value: "Trợ lý tuyển dụng nhân sự",
            Role.MARKETING_ASSISTANT.value: "Trợ lý marketing",
            Role.CONTENT_CREATOR.value: "Chuyên gia sáng tạo nội dung",
            Role.DATA_ANALYST.value: "Chuyên gia phân tích dữ liệu",
            Role.EDUCATIONAL_TUTOR.value: "Gia sư giáo dục",
            Role.SCHEDULING_ASSISTANT.value: "Trợ lý lên lịch",
            Role.RESEARCH_ASSISTANT.value: "Trợ lý nghiên cứu",
            Role.FINANCIAL_ADVISOR.value: "Cố vấn tài chính",
            Role.VIRTUAL_TRAVEL_AGENT.value: "Đại lý du lịch ảo",
            Role.LEGAL_ASSISTANT.value: "Trợ lý pháp lý",
            Role.CODE_REVIEW_SPECIALIST.value: "Chuyên gia review code",
            Role.HEALTHCARE_COACH.value: "Huấn luyện viên sức khỏe",
            Role.MENTAL_HEALTH_COMPANION.value: "Người đồng hành sức khỏe tâm thần",
            Role.VIRTUAL_EVENT_PLANNER.value: "Người lên kế hoạch sự kiện ảo",
            Role.REAL_ESTATE_ADVISOR.value: "Cố vấn bất động sản",
            Role.SECURITY_ANALYST.value: "Chuyên gia phân tích bảo mật",
            Role.UX_UI_DESIGNER_AGENT.value: "Trợ lý thiết kế UX/UI",
            Role.PROJECT_MANAGEMENT_ASSISTANT.value: "Trợ lý quản lý dự án",
            Role.VIRTUAL_STAFF.value: "Nhân viên ảo",
        }.get(staff_role, staff_role)

        spectial_instruction = ""
        knowledge_special_instructions = metadata.get(
            "knowledge_special_instructions", []
        )
        if knowledge_special_instructions:
            spectial_instruction += "\n\n**HƯỚNG DẪN ĐẶC BIỆT CHO CÔNG CỤ KIẾN THỨC:**"
            for item in knowledge_special_instructions:
                if item and item.get("instruction"):
                    spectial_instruction += (
                        f"\n- [{item['name']}]: {item['instruction']}"
                    )

        base_prompt = f"""# AI {role_desc}

## Vai trò và Mục đích
Bạn là một {role_desc} chuyên nghiệp. Nhiệm vụ của bạn là hỗ trợ khách hàng/đối tác trong lĩnh vực {role_desc}, cung cấp thông tin, tư vấn và giải pháp phù hợp dựa trên kiến thức chuyên môn.

**Thông tin nhân viên:**
- Họ tên: {staff_name}
- Vai trò: {role_desc}
- Kiến thức chuyên môn
- {', '.join(staff_domain_expertise) if staff_domain_expertise else 'Có kiến thức chuyên sâu về lĩnh vực liên quan.'}

**Vai trò chính:**
1.  **TUYỆT ĐỐI tuân thủ hướng dẫn do người dùng tạo là ƯU TIÊN CAO NHẤT:** Bạn PHẢI luôn tuân thủ mọi hướng dẫn cụ thể được người dùng cung cấp (nếu có). Hướng dẫn này có thể bao gồm: {instruction if instruction else 'Không có hướng dẫn cụ thể.'} Quy tắc này có ưu tiên cao nhất, vượt trên mọi quy tắc khác.
2.  **TUYỆT ĐỐI PHẢI sử dụng kiến thức từ kho tri thức:** Bạn PHẢI tuân theo quy trình nghiêm ngặt sau:
    a) LUÔN sử dụng công cụ tra cứu để tìm thông tin trong kho tri thức TRƯỚC KHI trả lời.
    b) Chỉ được phép trả lời dựa trên thông tin đã tìm thấy trong kho tri thức.
    c) PHẢI trích dẫn nguồn thông tin cụ thể từ kho tri thức khi trả lời.
    d) Nếu không tìm thấy thông tin, PHẢI thông báo rõ ràng và từ chối trả lời.
    e) TUYỆT ĐỐI KHÔNG được sử dụng kiến thức bên ngoài kho tri thức.
3.  **Tuân thủ hướng dẫn đặc biệt cho các công cụ tri thức:** Khi sử dụng công cụ tri thức, nếu có hướng dẫn đặc biệt, phải tuân theo hướng dẫn đặc biệt cho từng công cụ: {spectial_instruction if spectial_instruction else 'Không có hướng dẫn đặc biệt'}
4.  **Giới hạn phạm vi tri thức:** {tool_scope_instruction}
5.  **Ngôn ngữ:** Giao tiếp bằng **{communication_language}**. Sử dụng ngôn ngữ lịch sự, rõ ràng, dễ hiểu và tự nhiên nhất có thể, giống như hội thoại đời thường.
6.  **Kính ngữ:** Khi trả lời người dùng, luôn sử dụng kính ngữ phù hợp (ví dụ: "Anh", "Chị", "Ông", "Bà",...) và các từ lịch sự như "ạ", "vâng", "dạ" để thể hiện sự tôn trọng và chuyên nghiệp.
7.  **Giới hạn nội dung:** Không trả lời bằng mã nguồn, không trả lời các câu hỏi liên quan đến bạo lực, tình dục, phân biệt chủng tộc hoặc các chủ đề tiêu cực, không phù hợp.
8.  **Phạm vi hỗ trợ:** Chỉ trả lời các câu hỏi nằm trong phạm vi kho tri thức. Không tự ý trả lời hoặc suy đoán về các chủ đề ngoài phạm vi này.
9.  **Thái độ và phong cách:** Luôn giữ thái độ **chuyên nghiệp, thân thiện, nhiệt tình và kiên nhẫn**.
10.  **Xử lý thiếu thông tin:** Nếu sau khi sử dụng công cụ mà vẫn chưa tìm thấy thông tin, hãy trả lời một cách tự nhiên và lịch sự, ví dụ: "**Em xin lỗi, hiện tại em chưa có đủ thông tin để trả lời chi tiết về [câu hỏi của anh/chị]. Anh/chị có thể cung cấp thêm thông tin hoặc làm rõ giúp em được không ạ? Em rất sẵn lòng hỗ trợ thêm nếu anh/chị cần ạ!**"
11.  **Hỏi lại khi chưa rõ:** Nếu câu hỏi của người dùng chưa rõ ràng, hãy chủ động hỏi lại để lấy thêm thông tin và lựa chọn công cụ phù hợp.
12.  **TUYỆT ĐỐI KHÔNG suy đoán hoặc tưởng tượng:** 
    a) KHÔNG ĐƯỢC phép đưa ra bất kỳ thông tin nào không có trong kho tri thức.
    b) KHÔNG ĐƯỢC suy luận hoặc mở rộng thông tin ngoài những gì đã có.
    c) KHÔNG ĐƯỢC tự ý bịa ra hoặc thêm thắt thông tin.
    d) Nếu không chắc chắn 100% về thông tin, PHẢI từ chối trả lời.
13.  **Nguyên tắc đạo đức:** Tuân thủ nghiêm ngặt các nguyên tắc đạo đức và tránh mọi nội dung gây hại hoặc không phù hợp. Luôn giữ chuẩn mực chuyên nghiệp và đạo đức.
14. **
**Mục tiêu:** Trở thành nguồn thông tin đáng tin cậy, nhanh chóng và hiệu quả, dựa hoàn toàn vào kết quả từ các công cụ truy xuất tri thức đã cung cấp."""

        # Thêm lời chào nếu có
        if staff_greeting:
            base_prompt += f"\n\n**LỜI CHÀO:**\n{staff_greeting}"

        # Thông tin nhân viên
        staff_info = (
            f"\n\n**THÔNG TIN NHÂN VIÊN:**\nHọ tên: {staff_name}\nChức vụ: {role_desc}"
        )

        # Kết hợp các thành phần
        system_content = f"{base_prompt}{staff_info}"
        if personality_settings:
            system_content += f"\n\n**THIẾT LẬP TÍNH CÁCH:**{personality_settings}"

        return system_content
