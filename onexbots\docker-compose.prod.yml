services:
    staff_api:
        ports:
            - "8005:8005"
        environment:
            - LOG_LEVEL=info
        labels:
            # Bắt buộc để bật routing cho container
            - "traefik.enable=true"

            # HTTP router để redirect sang HTTPS
            - "traefik.http.routers.staff-api-http.rule=Host(`agent.onexbots.com`)"
            - "traefik.http.routers.staff-api-http.entrypoints=web"
            - "traefik.http.routers.staff-api-http.middlewares=https-redirect"

            # Middleware để chuyển sang HTTPS
            - "traefik.http.middlewares.https-redirect.redirectscheme.scheme=https"

            # HTTPS router dùng TLS
            - "traefik.http.routers.staff-api.rule=Host(`agent.onexbots.com`)"
            - "traefik.http.routers.staff-api.entrypoints=websecure"
            - "traefik.http.routers.staff-api.tls=true"
            - "traefik.http.routers.staff-api.tls.certresolver=le"

            # Chỉ định port nội bộ của app
            - "traefik.http.services.staff-api.loadbalancer.server.port=8005"
