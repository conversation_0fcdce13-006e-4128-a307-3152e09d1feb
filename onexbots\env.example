# API Settings
DEBUG=True
ENVIRONMENT=development

# CORS Settings
CORS_ORIGINS=["*"]

# Database Settings
POSTGRES_USER=onexbots
POSTGRES_PASSWORD=onexbots
POSTGRES_HOST=localhost
POSTGRES_PORT=54321
POSTGRES_DB=onexbots
POSTGRES_POOL_SIZE=5
POSTGRES_URL="postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}"
POSTGRES_ASYNC_URL="postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}"  
    
# ONEXSTAFFS API Settings
ONEXSTAFFS_API_URL=http://localhost:8001
ONEXSTAFFS_API_KEY=dev_key

# AWS Settings
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=r3ePOG+CAgGoS49cE96GGQ/NkVPZ3g4axVa9rfxn
AWS_REGION=ap-southeast-1



# Service-specific Settings
STAFF_API_HOST=0.0.0.0
STAFF_API_PORT=8000

RAG_API_HOST=0.0.0.0
RAG_API_PORT=8002

CHAT_API_HOST=0.0.0.0
CHAT_API_PORT=8003

OPENAI_API_KEY=***********************************************************************************************************************************************************************

ONEXBOTS_SERVICES_API_URL=https://api-staging.optiwarehouse.com/onexbots/services
COGNITO_CLIENT_ID=1l2idbgrda0hk6jil9fi5bgdhp
COGNITO_CLIENT_SECRET=9a3q4bdmt6807dbgs7ltml5ptoh406o3314bk83icagvbd2f4eo
COGNITO_TOKEN_URL=https://ap-southeast-1tmn6tbm0h.auth.ap-southeast-1.amazoncognito.com/oauth2/token
COGNITO_SCOPE=default-m2m-resource-server-ecjo6i/read

LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="pr-internal-theism-82"

# RAG PIPELINE SETTINGS
# S3 Settings
S3_BUCKET_NAME=optiwarehouse-onexbots-service-dev-knowledge-bucket
S3_PREFIX=knowledge/

# SQS Settings
SQS_QUEUE_URL=https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-onexbots-service-dev-knowledge-queue
SQS_RETRIEVER_QUEUE_URL=https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-resources-services-dev-retriever-queue
SQS_DLQ_QUEUE_URL=https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-onexbots-service-dev-knowledge-queue-dead-letter
SQS_DLQ_RETRIEVER_QUEUE_URL=https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-resources-services-dev-retriever-dead-letter-queue
SQS_MAX_MESSAGES=10
SQS_WAIT_TIME=20

# Processing Settings
MAX_FILE_SIZE_MB=50
SUPPORTED_FILE_TYPES=.pdf,.txt,.doc,.docx,.url

# Monitoring Settings
PROMETHEUS_ENABLED=True
METRICS_PORT=8003
