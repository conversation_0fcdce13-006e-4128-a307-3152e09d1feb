import logging
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from enum import Enum

from ..config import settings
from .base_api_service import BaseAPIService
from functools import lru_cache

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Tone(str, Enum):
    FRIENDLY = "friendly"
    PROFESSIONAL = "professional"
    CHEERFUL = "cheerful"
    ASSERTIVE = "assertive"
    HUMOROUS = "humorous"
    EMPATHETIC = "empathetic"


class Language(str, Enum):
    EN = "en"
    VI = "vi"
    ZH = "zh"
    JA = "ja"


class Temper(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class ResponseLength(str, Enum):
    SHORT = "short"
    MEDIUM = "medium"
    LONG = "long"


class PersonalTrait(BaseModel):
    formality: int = Field(default=50)
    detailed: int = Field(default=50)
    creativity: int = Field(default=50)


class InteractionStyle(BaseModel):
    tone: Optional[Tone] = None
    language: Language = Field(default=Language.VI)
    response_length: ResponseLength
    temp: Temper = Field(default=Temper.MEDIUM)


class LLMModel(BaseModel):
    provider: Optional[str] = None
    model_name: Optional[str] = None
    custom_url: Optional[str] = None


class LLMSettings(BaseModel):
    llm_api_key: Optional[str] = None
    llm_model: Optional[LLMModel] = None
    default_llm_temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    max_llm_call_retries: Optional[int] = None
    other_kwargs: Optional[Dict[str, Any]] = None


class PersonalitySettings(BaseModel):
    tone: Optional[str] = None
    language: str = Field(default="vi")
    personal_trait: Optional[PersonalTrait] = None
    farewell: Optional[str] = None
    ethical_constraints: Optional[bool] = None


class Knowledge(BaseModel):
    status: str
    s3_key: str
    created_at: str
    description: Optional[str] = None
    source: str
    user: Optional[str] = None
    virtual_staff_ids: Optional[List[str]] = None
    name: str
    meta_data: Optional[dict] = None
    id: str
    size: int
    summary_content: Optional[str] = None
    updated_at: str
    company_id: str


class KnowledgeBaseSettings(BaseModel):
    enabled: bool = Field(default=True)
    knowledge_ids: Optional[List[str]] = None
    domain_expertise_ids: Optional[List[str]] = None
    max_context_tokens: Optional[int] = None
    min_similarity_score: Optional[float] = None
    context_template: Optional[str] = None
    knowledge_list: Optional[List[Knowledge]] = Field(default_factory=list)


class ToolConfig(BaseModel):
    enabled: bool = Field(default=True)
    allowed_operations: Optional[List[str]] = None
    max_expression_length: Optional[int] = None
    max_number: Optional[int] = None
    api_key: Optional[str] = None
    allowed_locations: Optional[List[str]] = None
    cache_duration: Optional[int] = None
    max_results: Optional[int] = None
    safe_search: Optional[bool] = None


class ToolsSettings(BaseModel):
    enabled: Optional[List[str]] = None
    config: Optional[Dict[str, ToolConfig]] = None


class AdapterConfig(BaseModel):
    enabled: bool = Field(default=True)
    action: Optional[str] = None
    patterns: Optional[List[str]] = None
    max_messages: Optional[int] = None
    time_window: Optional[int] = None


class MCPSettings(BaseModel):
    enabled: bool = Field(default=True)
    server_url: Optional[str] = None
    enabled_adapters: Optional[List[str]] = None
    adapters_config: Optional[Dict[str, AdapterConfig]] = None


class ConversationSettings(BaseModel):
    max_history_length: Optional[int] = None
    context_window: Optional[int] = None
    response_timeout: Optional[int] = None
    fallback_message: Optional[str] = None


class Configuration(BaseModel):
    instruction: Optional[str] = None
    llm_settings: Optional[LLMSettings] = None
    personality: Optional[PersonalitySettings] = None
    knowledge_base: Optional[KnowledgeBaseSettings] = None
    tools: Optional[ToolsSettings] = None
    mcp: Optional[MCPSettings] = None
    conversation: Optional[ConversationSettings] = None

    model_config = {"arbitrary_types_allowed": True, "extra": "allow"}


class Role(str, Enum):
    CUSTOMER_SUPPORT_AGENT = "CUSTOMER_SUPPORT_AGENT"
    SALES_ASSISTANT = "SALES_ASSISTANT"
    VIRTUAL_PERSONAL_ASSISTANT = "VIRTUAL_PERSONAL_ASSISTANT"
    TECHNICAL_SUPPORT_SPECIALIST = "TECHNICAL_SUPPORT_SPECIALIST"
    HR_RECRUITMENT_ASSISTANT = "HR_RECRUITMENT_ASSISTANT"
    MARKETING_ASSISTANT = "MARKETING_ASSISTANT"
    CONTENT_CREATOR = "CONTENT_CREATOR"
    DATA_ANALYST = "DATA_ANALYST"
    EDUCATIONAL_TUTOR = "EDUCATIONAL_TUTOR"
    SCHEDULING_ASSISTANT = "SCHEDULING_ASSISTANT"
    RESEARCH_ASSISTANT = "RESEARCH_ASSISTANT"
    FINANCIAL_ADVISOR = "FINANCIAL_ADVISOR"
    VIRTUAL_TRAVEL_AGENT = "VIRTUAL_TRAVEL_AGENT"
    LEGAL_ASSISTANT = "LEGAL_ASSISTANT"
    CODE_REVIEW_SPECIALIST = "CODE_REVIEW_SPECIALIST"
    HEALTHCARE_COACH = "HEALTHCARE_COACH"
    MENTAL_HEALTH_COMPANION = "MENTAL_HEALTH_COMPANION"
    VIRTUAL_EVENT_PLANNER = "VIRTUAL_EVENT_PLANNER"
    REAL_ESTATE_ADVISOR = "REAL_ESTATE_ADVISOR"
    SECURITY_ANALYST = "SECURITY_ANALYST"
    UX_UI_DESIGNER_AGENT = "UX_UI_DESIGNER_AGENT"
    PROJECT_MANAGEMENT_ASSISTANT = "PROJECT_MANAGEMENT_ASSISTANT"
    VIRTUAL_STAFF = "VIRTUAL_STAFF"


class VirtualStaff(BaseModel):
    """Virtual staff configuration model."""

    company_id: str
    id: str
    name: str
    department_id: str
    image: Optional[str] = None
    role: Role
    skills: Optional[List[str]] = None
    greeting: Optional[str] = None
    domain_expertise: Optional[List[str]] = None
    configuration: Configuration
    model_config = {"arbitrary_types_allowed": True, "extra": "allow"}


class VirtualStaffService(BaseAPIService[Dict[str, Any]]):
    """Service for interacting with the Virtual Staff API."""

    def __init__(self):
        super().__init__()
        logger.info("Initialized VirtualStaffService")

    def create(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new virtual staff member."""
        return self._make_request("POST", "/virtual-staff", json=data)

    def get(self, id: str) -> Optional[Dict[str, Any]]:
        logger.info("Virtual staff API called")
        """Get a virtual staff member by ID."""
        return self._make_request("GET", f"/virtual-staff/{id}")

    def list(
        self, department_id: Optional[str] = None
    ) -> Optional[List[Dict[str, Any]]]:
        """List virtual staff members, optionally filtered by department."""
        if department_id:
            return self._make_request(
                "GET", f"/virtual-staff/department/{department_id}"
            )
        return self._make_request("GET", "/virtual-staff")

    def update(self, id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a virtual staff member."""
        return self._make_request("PUT", f"/virtual-staff/{id}", json=data)

    def delete(self, id: str) -> bool:
        """Delete a virtual staff member."""
        result = self._make_request("DELETE", f"/virtual-staff/{id}")
        return result is not None

    def remove_knowledge(self, staff_id: str, knowledge_id: str) -> bool:
        """Remove knowledge from a virtual staff member."""
        result = self._make_request(
            "DELETE", f"/virtual-staff/{staff_id}/knowledge/{knowledge_id}"
        )
        return result is not None

    async def get_staff_config(self, staff_id: str) -> Dict[str, Any]:
        """Get staff configuration by ID."""
        try:
            staff_data = get_virtual_staff(staff_id)
            if not staff_data:
                # Fallback to test config if staff not found
                from onexbots.staff_api.app.api.v1.endpoints.config import TEST_CONFIG

                return TEST_CONFIG
            return staff_data.get("data", {})
        except Exception as e:
            logger.error(f"Error getting staff config: {str(e)}")
            # Fallback to test config on error
            from onexbots.staff_api.app.api.v1.endpoints.config import TEST_CONFIG

            return TEST_CONFIG


# Singleton instance
virtual_staff_service = VirtualStaffService()


# @lru_cache(maxsize=128)
def get_virtual_staff(staff_id: str) -> Optional[Dict[str, Any]]:
    """Get a virtual staff member by ID with shared in-memory cache."""
    return virtual_staff_service.get(staff_id)
