"""
Shared configuration for ONEXBOTS microservices.
"""

import os
from functools import lru_cache
from typing import List
from pathlib import Path
from enum import Enum
from pydantic_settings import BaseSettings, SettingsConfigDict


class Environment(str, Enum):
    """Application environment types.

    Defines the possible environments the application can run in:
    development, staging, production, and test.
    """

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TEST = "test"


class SharedSettings(BaseSettings):
    """Base settings for all ONEXBOTS services."""

    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "ONEXBOTS"
    DEBUG: bool = True
    ENVIRONMENT: Environment = Environment.DEVELOPMENT

    # CORS Settings
    CORS_ORIGINS: List[str] = ["*"]

    # Database Settings
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_HOST: str
    POSTGRES_PORT: int
    POSTGRES_DB: str
    POSTGRES_POOL_SIZE: int = 5
    POSTGRES_URL: str
    POSTGRES_ASYNC_URL: str
    # ONEXSTAFFS API Settings
    ONEXSTAFFS_API_URL: str
    ONEXSTAFFS_API_KEY: str

    # AWS Settings
    AWS_ACCESS_KEY_ID: str
    AWS_SECRET_ACCESS_KEY: str
    AWS_REGION: str
    # OpenAI Settings
    OPENAI_API_KEY: str
    EMBEDDING_MODEL: str = "text-embedding-ada-002"

    # Staff API specific settings
    STAFF_API_HOST: str = "0.0.0.0"
    STAFF_API_PORT: int = 8000

    # Service-specific Settings
    RAG_API_HOST: str = "0.0.0.0"
    RAG_API_PORT: int = 8002

    CHAT_API_HOST: str = "0.0.0.0"
    CHAT_API_PORT: int = 8003

    # ONEXBOTS Services API Settings
    ONEXBOTS_SERVICES_API_URL: str = (
        "https://api-staging.optiwarehouse.com/onexbots/services"
    )
    OPTIWAREHOUSE_SERVICES_API_URL: str = (
        "https://api-staging.optiwarehouse.com/"
    )

    COGNITO_CLIENT_ID: str
    COGNITO_CLIENT_SECRET: str
    COGNITO_TOKEN_URL: str = (
        "https://ap-southeast-1tmn6tbm0h.auth.ap-southeast-1.amazoncognito.com/oauth2/token"
    )
    COGNITO_SCOPE: str = "default-m2m-resource-server-ecjo6i/read"

    LANGSMITH_TRACING: bool
    LANGSMITH_ENDPOINT: str
    LANGSMITH_API_KEY: str
    LANGSMITH_PROJECT: str

    model_config = SettingsConfigDict(
        env_file=str(Path(__file__).parent.parent / ".env"),
        env_file_encoding="utf-8",
        case_sensitive=True,
        env_nested_delimiter="__",
        validate_default=True,
        extra="ignore",
    )


@lru_cache
def get_settings() -> SharedSettings:
    """Get cached settings instance."""
    return SharedSettings()


settings = get_settings()
