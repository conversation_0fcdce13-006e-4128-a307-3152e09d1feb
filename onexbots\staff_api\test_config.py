import os
from pathlib import Path
from onexbots.shared.config import Settings
from onexbots.staff_api.app.core.config import settings as staff_settings

def main():
    # Check if .env file exists in the onexbots directory
    env_path = Path(__file__).parent.parent / ".env"
    if not env_path.exists():
        print("Error: .env file not found in onexbots directory!")
        print("Please create a .env file in the onexbots directory with the following variables:")
        print("""
# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=ONEXBOTS
DEBUG=True
ENVIRONMENT=development

# CORS Settings
CORS_ORIGINS=["*"]

# Database Settings
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=onexbots

# ONEXSTAFFS API Settings
ONEXSTAFFS_API_URL=http://localhost:8001
ONEXSTAFFS_API_KEY=your_api_key_here

# AWS Settings
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1

# Service-specific Settings
STAFF_API_HOST=0.0.0.0
STAFF_API_PORT=8000
""")
        return

    # Check if dependencies are installed
    try:
        import fastapi
        import uvicorn
        import pydantic
        import sqlalchemy
        import boto3
        print("All required dependencies are installed!")
    except ImportError as e:
        print(f"Error: Missing dependency - {e}")
        print("Please install the required dependencies:")
        print("  uv pip install .")
        return

    # Test shared config loading
    try:
        shared_settings = Settings()
        print("\nShared Config Test:")
        print(f"Project Name: {shared_settings.PROJECT_NAME}")
        print(f"API Version: {shared_settings.API_V1_STR}")
        print(f"Database Host: {shared_settings.POSTGRES_HOST}")
        print(f"Database Port: {shared_settings.POSTGRES_PORT}")
    except Exception as e:
        print(f"\nError loading shared config: {e}")
        return

    # Test staff config loading
    try:
        print("\nStaff API Config Test:")
        print(f"Staff API Host: {staff_settings.STAFF_API_HOST}")
        print(f"Staff API Port: {staff_settings.STAFF_API_PORT}")
        print(f"Staff Project Name: {staff_settings.PROJECT_NAME}")
        print(f"Database Settings (inherited): {staff_settings.POSTGRES_HOST}:{staff_settings.POSTGRES_PORT}")
    except Exception as e:
        print(f"\nError loading staff config: {e}")
        return

    print("\nEnvironment setup is complete and all configurations are loaded successfully!")

if __name__ == "__main__":
    main()