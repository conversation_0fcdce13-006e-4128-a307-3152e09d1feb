from typing import Optional
from pydantic import BaseModel, Field


class ErrorResponse(BaseModel):
    detail: str = Field(..., description="Error message")


class ChatRequest(BaseModel):
    message: str = Field(..., description="The message to send to the staff")
    user_id: Optional[str] = Field(None, description="Existing user ID (optional)")
    phone_number: Optional[str] = Field(
        None, description="User's phone number (required if no user_id)"
    )
    name: Optional[str] = Field(
        None, description="User's name (required if no user_id)"
    )
    connection_id: Optional[str] = Field(
        None, description="Connection ID for the chat (optional)"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "message": "Hello, I need help with my order",
                "phone_number": "+1234567890",
                "name": "<PERSON>",
            }
        }


class ChatResponse(BaseModel):
    response: str = Field(..., description="AI's response to the message")
    conversation_id: str = Field(
        ..., description="Unique identifier for the conversation"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "response": "I'd be happy to help you with your order. Could you please provide your order number?",
                "conversation_id": "123e4567-e89b-12d3-a456-426614174000",
            }
        }


class ChatAsyncRequest(BaseModel):
    """Request model for asynchronous chat requests."""
    message: str = Field(..., description="The message to send to the staff")
    connection_id: str = Field(..., description="Connection ID for the chat")
    external_user_id: str = Field(..., description="External user ID")
    channel_id: str = Field(..., description="Channel ID (Zalo_OA id, Facebook_Page id, etc.)")
    phone_number: Optional[str] = Field(
        None, description="User's phone number (optional)"
    )
    name: Optional[str] = Field(
        None, description="User's name (optional)"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "message": "Hello, I need help with my order",
                "connection_id": "abc-123-xyz",
                "external_user_id": "user-123",
                "phone_number": "+1234567890",
                "name": "John Doe",
                "channel_id": "channel-123"
            }
        }


class ChatAsyncResponse(BaseModel):
    """Response model for asynchronous chat requests."""
    message: str = Field(..., description="Status message")

    class Config:
        json_schema_extra = {
            "example": {
                "message": "Request accepted for processing"
            }
        }


class EmbedResponse(BaseModel):
    script: str = Field(
        ..., description="HTML script for embedding the staff chat widget"
    )

    class Config:
        json_schema_extra = {
            "example": {"script": "<script>window.ONEXBOTS_CONFIG = {...};</script>"}
        }
