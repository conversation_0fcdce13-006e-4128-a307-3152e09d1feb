# Staff API

This is the Staff API service for ONEXBOTS, built with FastAPI.

## Prerequisites

- Python 3.11+
- uv package manager
- Virtual environment (recommended)

## Setup

1. Make sure you are in the `onexbots/staff_api` directory
2. Create and activate a virtual environment (if not already):
   ```bash
   uv venv
   source .venv/bin/activate  # On Unix/macOS
   # or
   source .venv/Scripts/activate  # On Windows
   ```
3. Install the package and its dependencies:
   ```bash
   uv pip install -e .
   ```

## Running the Application

1. Make sure you are in the `onexbots/staff_api` directory
2. Make the run script executable (if not already):
   ```bash
   chmod +x run.sh
   ```
3. Run the application:
   ```bash
   ./run.sh
   ```

The API will be available at:
- API documentation: http://localhost:8000/docs
- ReDoc documentation: http://localhost:8000/redoc
- API endpoints: http://localhost:8000/api/v1

## Development

- The application runs in development mode with auto-reload enabled
- Changes to the code will automatically restart the server
- The server runs on all interfaces (0.0.0.0) on port 8000

## Features

- Staff chat integration
- Staff configuration management
- Real-time chat processing
- AWS SQS integration for message processing
- PostgreSQL database integration

## Development Setup

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- PostgreSQL
- AWS CLI (for SQS/S3 integration)
- uv package manager

### Environment Setup

#### Required Environment Variables

Create a `.env` file in the service directory with the following variables:

```bash
# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=ONEXBOTS
DEBUG=True
ENVIRONMENT=development

# CORS Settings
CORS_ORIGINS=["*"]

# Database Settings
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=onexbots

# ONEXSTAFFS API Settings
ONEXSTAFFS_API_URL=http://localhost:8001
ONEXSTAFFS_API_KEY=your_api_key_here

# AWS Settings
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1

# Service-specific Settings
STAFF_API_HOST=0.0.0.0
STAFF_API_PORT=8000
```

#### Development Setup

1. Create and activate virtual environment:
   ```bash
   uv venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

2. Install the package in development mode with all dependencies:
   ```bash
   uv pip install -e ".[dev]"
   ```

3. Set up pre-commit hooks:
   ```bash
   pre-commit install
   ```

### Development Tools

The service includes several development tools:

- **Testing**: pytest, pytest-asyncio, pytest-cov
- **Code Quality**: black, isort, flake8, mypy
- **Documentation**: mkdocs, mkdocs-material
- **Development**: ipython, jupyter, pre-commit

### Code Quality

The service uses several tools to maintain code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Static type checking
- **pre-commit**: Git hooks for code quality

These tools are configured in:
- `pyproject.toml`: Tool configurations
- `.pre-commit-config.yaml`: Pre-commit hooks

### Running Locally

```bash
# Start the service
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### API Documentation

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Testing

```bash
# Run tests
pytest

# Run tests with coverage
pytest --cov=staff_api

# Run specific test file
pytest tests/test_staff.py
```

## Contributing

Please follow the project's code style and testing guidelines when contributing to this service. 