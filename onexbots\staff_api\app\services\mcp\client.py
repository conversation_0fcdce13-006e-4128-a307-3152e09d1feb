import logging
from typing import Dict, Any, List, Optional
import httpx
from langchain_core.messages import BaseMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MCPClient:
    """Client for interacting with the Model Context Protocol server."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip("/")
        self.client = httpx.AsyncClient(base_url=base_url)
    
    async def process_messages(
        self,
        messages: List[BaseMessage],
        user_id: str,
        config: Optional[Dict[str, Any]] = None
    ) -> List[BaseMessage]:
        """Process messages through MCP server."""
        try:
            # Convert messages to dict format
            message_dicts = []
            for msg in messages:
                msg_dict = {
                    "type": msg.__class__.__name__.lower().replace("message", ""),
                    "content": msg.content
                }
                message_dicts.append(msg_dict)
            
            # Make request to MCP server
            response = await self.client.post(
                "/mcp/process",
                json={
                    "messages": message_dicts,
                    "config": config or {},
                    "user_id": user_id
                }
            )
            response.raise_for_status()
            
            # Convert response back to LangChain messages
            result = response.json()
            return self._convert_to_langchain_messages(result["messages"])
            
        except Exception as e:
            logger.error(f"Error processing messages: {str(e)}")
            return messages
    
    async def get_context(self, user_id: str) -> Dict[str, Any]:
        """Get user context from MCP server."""
        try:
            response = await self.client.get(f"/mcp/context/{user_id}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting context: {str(e)}")
            return {}
    
    async def clear_context(self, user_id: str):
        """Clear user context from MCP server."""
        try:
            response = await self.client.delete(f"/mcp/context/{user_id}")
            response.raise_for_status()
        except Exception as e:
            logger.error(f"Error clearing context: {str(e)}")
    
    def _convert_to_langchain_messages(self, messages: List[Dict[str, Any]]) -> List[BaseMessage]:
        """Convert dict messages to LangChain format."""
        from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
        
        converted = []
        for msg in messages:
            msg_type = msg.get("type", "human")
            content = msg.get("content", "")
            
            if msg_type == "human":
                converted.append(HumanMessage(content=content))
            elif msg_type == "ai":
                converted.append(AIMessage(content=content))
            elif msg_type == "system":
                converted.append(SystemMessage(content=content))
        
        return converted
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose() 