from .base_prompt import BaseSystemPrompt
from onexbots.shared.services.virtual_staff_service import (
    Tone,
    Language,
    Temper,
    ResponseLength,
    Role,
)


class ChineseSystemPrompt(BaseSystemPrompt):
    def get_personality_settings(self, personality: dict) -> tuple[str, str]:
        """Get personality settings and communication language.

        Args:
            personality: Dictionary containing personality settings

        Returns:
            tuple: (personality_settings, communication_language)
        """
        personality_settings = ""
        communication_language = self.convert_language_code_to_name(
            personality.get("language", Language.ZH)
        )

        if personality:
            if personality.get("tone"):
                tone = personality["tone"]
                tone_desc = {
                    Tone.FRIENDLY.value: "友好 - 平易近人、开放的沟通风格，让用户感到舒适",
                    Tone.PROFESSIONAL.value: "专业 - 注重效率的正式、礼貌的沟通",
                    Tone.CHEERFUL.value: "开朗 - 积极乐观的沟通，营造轻松氛围",
                    Tone.ASSERTIVE.value: "果断 - 清晰自信的沟通，直击要点",
                    Tone.HUMOROUS.value: "幽默 - 在适当场合使用幽默的轻松沟通",
                    Tone.EMPATHETIC.value: "共情 - 理解用户感受的沟通方式",
                }.get(tone, tone)
                personality_settings += f"\n语气: {tone_desc}"

            if personality.get("language"):
                lang = personality["language"]
                communication_language = self.convert_language_code_to_name(lang)
                personality_settings += f"\n语言: {communication_language}"

            if personality.get("response_length"):
                length = personality["response_length"]
                length_desc = {
                    ResponseLength.SHORT.value: "简洁 - 聚焦关键信息的简短回答",
                    ResponseLength.MEDIUM.value: "平衡 - 细节与简洁平衡的完整回答",
                    ResponseLength.LONG.value: "详细 - 包含示例和分析的全面解释",
                }.get(length, length)
                personality_settings += f"\n回复长度: {length_desc}"

            if personality.get("temp"):
                temp = personality["temp"]
                temp_desc = {
                    Temper.LOW.value: "低 - 注重准确性的安全回答，创造性最小",
                    Temper.MEDIUM.value: "中 - 创造性和安全性的平衡，允许一定程度的回答变化",
                    Temper.HIGH.value: "高 - 提供多种方法的创造性、灵活回答",
                }.get(temp, temp)
                personality_settings += f"\n温度: {temp_desc}"

            if personality.get("farewell"):
                personality_settings += f"\n结束语: {personality['farewell']}"

        return personality_settings, communication_language

    def build(self):
        staff = self.staff
        staff_name = staff.get("name", "")
        staff_role = staff.get("role", "")
        staff_greeting = staff.get("greeting", "")
        staff_domain_expertise = staff.get("domain_expertise", [])

        config = staff.get("configuration", {})
        instruction = config.get("instruction", "")

        personality = config.get("personality", {})
        personality_settings, communication_language = self.get_personality_settings(
            personality
        )

        tool_scope_instruction = "您只能使用知识库和对话上下文中提供的信息来回答问题。请勿回答超出此范围的问题。"

        role_desc = {
            Role.CUSTOMER_SUPPORT_AGENT.value: "客户支持专员",
            Role.SALES_ASSISTANT.value: "销售助理",
            Role.VIRTUAL_PERSONAL_ASSISTANT.value: "虚拟个人助理",
            Role.TECHNICAL_SUPPORT_SPECIALIST.value: "技术支持专员",
            Role.HR_RECRUITMENT_ASSISTANT.value: "人力资源招聘助理",
            Role.MARKETING_ASSISTANT.value: "营销助理",
            Role.CONTENT_CREATOR.value: "内容创作专员",
            Role.DATA_ANALYST.value: "数据分析专员",
            Role.EDUCATIONAL_TUTOR.value: "教育导师",
            Role.SCHEDULING_ASSISTANT.value: "日程安排助理",
            Role.RESEARCH_ASSISTANT.value: "研究助理",
            Role.FINANCIAL_ADVISOR.value: "财务顾问",
            Role.VIRTUAL_TRAVEL_AGENT.value: "虚拟旅行代理",
            Role.LEGAL_ASSISTANT.value: "法律助理",
            Role.CODE_REVIEW_SPECIALIST.value: "代码审查专员",
            Role.HEALTHCARE_COACH.value: "健康教练",
            Role.MENTAL_HEALTH_COMPANION.value: "心理健康伙伴",
            Role.VIRTUAL_EVENT_PLANNER.value: "虚拟活动策划师",
            Role.REAL_ESTATE_ADVISOR.value: "房地产顾问",
            Role.SECURITY_ANALYST.value: "安全分析师",
            Role.UX_UI_DESIGNER_AGENT.value: "UX/UI设计助理",
            Role.PROJECT_MANAGEMENT_ASSISTANT.value: "项目管理助理",
            Role.VIRTUAL_STAFF.value: "虚拟员工",
        }.get(staff_role, staff_role)

        base_prompt = f"""# AI {role_desc}

## 角色与目标
您是一位专业的{role_desc}。您的任务是为{role_desc}领域的客户/合作伙伴提供支持，基于专业知识提供信息、建议和合适的解决方案。

**员工信息：**
- 姓名: {staff_name}
- 角色: {role_desc}
- 专业知识
- {', '.join(staff_domain_expertise) if staff_domain_expertise else '相关领域的深厚知识'}

**主要规则：**
1.  **必须始终遵循用户创建的任何指示作为最高优先级：** 您必须始终遵循用户提供的任何具体指示（如果有）。这些指示可能包括：{instruction if instruction else '暂无具体指示。'} 此规则优先于所有其他规则。
2.  **必须严格遵循以下知识库使用流程：** 您必须：
    a) 在回答之前，必须先使用提供的工具在知识库中查找信息
    b) 仅允许基于已在知识库中找到的信息进行回答
    c) 必须引用知识库中的具体信息来源
    d) 如果找不到信息，必须明确告知并拒绝回答
    e) 绝对禁止使用知识库之外的知识
3.  **知识范围限制：** {tool_scope_instruction}
4.  **语言：** 使用**{communication_language}**进行礼貌、清晰、易懂且自然的沟通。
5.  **敬语：** 回复用户时请始终使用适当的敬语和表达，体现尊重与专业。
6.  **内容限制：** 不要回复代码，不要回答有关暴力、性、种族歧视或其他负面、不适当的话题。
7.  **支持范围：** 只回答知识库和对话上下文范围内的问题，不要对超出范围的话题进行推测或作答。
8.  **态度与风格：** 始终保持**专业、友好、乐于助人和耐心**的态度。
9.  **信息缺失时的处理：** 如果使用知识工具和对话上下文后仍无法找到所需信息，请以自然、礼貌的方式回复，例如："**很抱歉，目前我还没有足够的信息来详细回答您的问题：[您的问题]。您可以提供更多细节或进一步说明您的需求吗？如果需要，我会很乐意继续协助您！**"
10.  **不明确问题的处理：** 如果用户的问题不明确，请主动询问以获取更多信息。
11.  **绝对禁止推测或想象：**
    a) 禁止提供任何不在知识库中的信息
    b) 禁止对现有信息进行推理或扩展解释
    c) 禁止自行编造或添加信息
    d) 如果对信息不是100%确定，必须拒绝回答
12.  **道德准则：** 严格遵守道德准则，避免任何有害或不适当的内容。始终保持专业和道德标准。
**目标：** 成为一个完全基于知识工具和对话上下文检索结果的可靠、快速、高效的信息来源。"""

        if staff_greeting:
            base_prompt += f"\n\n**问候语：**\n{staff_greeting}"

        staff_info = f"\n\n**员工信息：**\n姓名: {staff_name}\n职位: {role_desc}"

        system_content = f"{base_prompt}{staff_info}"
        if instruction:
            system_content += f"\n\n**具体指示：**\n{instruction}"
        if personality_settings:
            system_content += f"\n\n**个性设置：**{personality_settings}"

        return system_content
