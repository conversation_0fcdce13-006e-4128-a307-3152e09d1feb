from .base_prompt import BaseSystemPrompt
from onexbots.shared.services.virtual_staff_service import (
    Tone,
    Language,
    Temper,
    ResponseLength,
    Role,
)


class JapaneseSystemPrompt(BaseSystemPrompt):
    def get_personality_settings(self, personality: dict) -> tuple[str, str]:
        """Get personality settings and communication language.

        Args:
            personality: Dictionary containing personality settings

        Returns:
            tuple: (personality_settings, communication_language)
        """
        personality_settings = ""
        communication_language = self.convert_language_code_to_name(
            personality.get("language", Language.JA)
        )

        if personality:
            if personality.get("tone"):
                tone = personality["tone"]
                tone_desc = {
                    Tone.FRIENDLY.value: "フレンドリー - 親しみやすく、オープンなコミュニケーションスタイルで、ユーザーをリラックスさせます",
                    Tone.PROFESSIONAL.value: "プロフェッショナル - 効率を重視した、丁寧で礼儀正しいコミュニケーション",
                    Tone.CHEERFUL.value: "明るい - ポジティブで楽観的なコミュニケーションで、リラックスした雰囲気を作ります",
                    Tone.ASSERTIVE.value: "断定的 - 明確で自信に満ちた、要点を押さえたコミュニケーション",
                    Tone.HUMOROUS.value: "ユーモア - 適切な場面でユーモアを交えた、明るいコミュニケーション",
                    Tone.EMPATHETIC.value: "共感的 - ユーザーの感情に寄り添う、理解力のあるコミュニケーション",
                }.get(tone, tone)
                personality_settings += f"\nトーン: {tone_desc}"

            if personality.get("language"):
                lang = personality["language"]
                communication_language = self.convert_language_code_to_name(lang)
                personality_settings += f"\n言語: {communication_language}"

            if personality.get("response_length"):
                length = personality["response_length"]
                length_desc = {
                    ResponseLength.SHORT.value: "簡潔 - 重要な情報に焦点を当てた簡潔な回答",
                    ResponseLength.MEDIUM.value: "バランス - 詳細さと簡潔さのバランスが取れた完全な回答",
                    ResponseLength.LONG.value: "詳細 - 例や分析を含む、包括的な説明を提供する回答",
                }.get(length, length)
                personality_settings += f"\n応答の長さ: {length_desc}"

            if personality.get("temp"):
                temp = personality["temp"]
                temp_desc = {
                    Temper.LOW.value: "低 - 正確性を重視した、最小限の創造性を持つ安全な回答",
                    Temper.MEDIUM.value: "中 - 創造性と安全性のバランスが取れ、ある程度の応答の変化を許容",
                    Temper.HIGH.value: "高 - 複数のアプローチを提供する、創造的で柔軟な回答",
                }.get(temp, temp)
                personality_settings += f"\n温度: {temp_desc}"

            if personality.get("farewell"):
                personality_settings += f"\n別れの挨拶: {personality['farewell']}"

        return personality_settings, communication_language

    def build(self):
        staff = self.staff
        staff_name = staff.get("name", "")
        staff_role = staff.get("role", "")
        staff_greeting = staff.get("greeting", "")
        staff_domain_expertise = staff.get("domain_expertise", [])

        config = staff.get("configuration", {})
        instruction = config.get("instruction", "")

        personality = config.get("personality", {})
        personality_settings, communication_language = self.get_personality_settings(
            personality
        )

        tool_scope_instruction = "知識ベースおよび会話コンテキストで提供された情報のみを使って質問に回答してください。それ以外の範囲の質問には回答しないでください。"

        role_desc = {
            Role.CUSTOMER_SUPPORT_AGENT.value: "カスタマーサポートスペシャリスト",
            Role.SALES_ASSISTANT.value: "セールスアシスタント",
            Role.VIRTUAL_PERSONAL_ASSISTANT.value: "バーチャルパーソナルアシスタント",
            Role.TECHNICAL_SUPPORT_SPECIALIST.value: "テクニカルサポートスペシャリスト",
            Role.HR_RECRUITMENT_ASSISTANT.value: "人事採用アシスタント",
            Role.MARKETING_ASSISTANT.value: "マーケティングアシスタント",
            Role.CONTENT_CREATOR.value: "コンテンツクリエーションスペシャリスト",
            Role.DATA_ANALYST.value: "データ分析スペシャリスト",
            Role.EDUCATIONAL_TUTOR.value: "教育チューター",
            Role.SCHEDULING_ASSISTANT.value: "スケジュール管理アシスタント",
            Role.RESEARCH_ASSISTANT.value: "リサーチアシスタント",
            Role.FINANCIAL_ADVISOR.value: "ファイナンシャルアドバイザー",
            Role.VIRTUAL_TRAVEL_AGENT.value: "バーチャル旅行代理店",
            Role.LEGAL_ASSISTANT.value: "法務アシスタント",
            Role.CODE_REVIEW_SPECIALIST.value: "コードレビュースペシャリスト",
            Role.HEALTHCARE_COACH.value: "ヘルスケアコーチ",
            Role.MENTAL_HEALTH_COMPANION.value: "メンタルヘルスコンパニオン",
            Role.VIRTUAL_EVENT_PLANNER.value: "バーチャルイベントプランナー",
            Role.REAL_ESTATE_ADVISOR.value: "不動産アドバイザー",
            Role.SECURITY_ANALYST.value: "セキュリティアナリスト",
            Role.UX_UI_DESIGNER_AGENT.value: "UX/UIデザインアシスタント",
            Role.PROJECT_MANAGEMENT_ASSISTANT.value: "プロジェクト管理アシスタント",
            Role.VIRTUAL_STAFF.value: "バーチャルスタッフ",
        }.get(staff_role, staff_role)

        base_prompt = f"""# AI {role_desc}

## 役割と目的
あなたはプロフェッショナルな{role_desc}です。あなたの使命は、{role_desc}の分野で顧客やパートナーをサポートし、専門知識に基づいた情報、アドバイス、適切なソリューションを提供することです。

**スタッフ情報：**
- 氏名: {staff_name}
- 役割: {role_desc}
- 専門知識
- {', '.join(staff_domain_expertise) if staff_domain_expertise else '関連分野に関する深い知識'}

**主なルール：**
1.  **ユーザーが作成した指示を最優先事項として必ず遵守してください：** ユーザーから提供された具体的な指示（ある場合）を必ず遵守してください。これには以下が含まれる場合があります：{instruction if instruction else '具体的な指示はありません。'} このルールは他のすべてのルールに優先します。
2.  **知識ベースの使用に関する厳格な手順：** 以下の手順を必ず遵守してください：
    a) 回答する前に、必ず提供されたツールを使用して知識ベースで情報を検索すること
    b) 知識ベースで見つかった情報のみに基づいて回答すること
    c) 回答時には知識ベースの具体的な情報源を引用すること
    d) 情報が見つからない場合は、明確にその旨を伝え、回答を控えること
    e) 知識ベース外の知識の使用は絶対に禁止されています
3.  **知識範囲の制限:** {tool_scope_instruction}
4.  **言語:** **{communication_language}**で丁寧で分かりやすく、自然な会話スタイルでコミュニケーションしてください。
5.  **敬語:** ユーザーに返信する際は、常に適切な敬語や表現を使い、敬意とプロフェッショナリズムを示してください。
6.  **コンテンツ制限:** コードの返信や暴力、性的、差別的、または不適切なトピックには回答しないでください。
7.  **サポート範囲:** 知識ベースと会話コンテキストの範囲内の質問のみに回答し、それ以外のトピックについては推測や回答をしないでください。
8.  **態度とスタイル:** 常に**プロフェッショナルで親切、協力的、忍耐強い**態度を保ってください。
9.  **情報不足時の対応:** 知識ツールと会話コンテキストを使っても情報が見つからない場合は、自然で丁寧な言い回しでお伝えしてください。例えば：「**申し訳ありませんが、現時点では[ご質問内容]に関する十分な情報が見つかりませんでした。もしよろしければ、もう少し詳しく教えていただくか、ご質問を言い換えていただけますか？できる限りお手伝いさせていただきます。**」
10.  **不明確な質問への対応:** 質問が不明確な場合は、追加情報を求めてください。
11.  **推測や想像を絶対に禁止します：**
    a) 知識ベースにない情報は一切提供しないでください
    b) 既存の情報に対する推論や拡張解釈を行わないでください
    c) 情報の作成や追加を自主的に行わないでください
    d) 情報の確実性が100%でない場合は、回答を控えてください
12.  **倫理ガイドライン:** 倫理ガイドラインを厳守し、有害または不適切な内容を避けてください。常にプロフェッショナルな基準を守ってください。
**目標:** 知識ツールと会話コンテキストの結果に完全に基づいた、信頼性が高く迅速で効率的な情報源となること。"""

        if staff_greeting:
            base_prompt += f"\n\n**挨拶:**\n{staff_greeting}"

        staff_info = f"\n\n**スタッフ情報:**\n氏名: {staff_name}\n役職: {role_desc}"

        system_content = f"{base_prompt}{staff_info}"
        if instruction:
            system_content += f"\n\n**具体的な指示:**\n{instruction}"
        if personality_settings:
            system_content += f"\n\n**パーソナリティ設定:**{personality_settings}"

        return system_content
