from functools import lru_cache
from pathlib import Path
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import List, Optional

class Settings(BaseSettings):
    """RAG Pipeline specific settings."""
    # Project Information
    PROJECT_NAME: str = "RAG Pipeline Service"
    PROJECT_DESCRIPTION: str = "A FastAPI-based service for processing documents and generating embeddings using RAG (Retrieval-Augmented Generation) pipeline."
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # API Settings
    API_V1_STR: str = "/api/v1"
    HOST: str = "0.0.0.0"
    PORT: int = 8002
    CORS_ORIGINS: List[str] = ["*"]
    
    # S3 Settings
    S3_BUCKET_NAME: str = "onexbots-staging"
    S3_PREFIX: str = "knowledge-base/"
    
    # SQS Settings
    SQS_QUEUE_URL: str = "https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-onexbots-service-dev-knowledge-queue"
    SQS_RETRIEVER_QUEUE_URL: str = "https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-resources-services-dev-retriever-queue"
    SQS_DLQ_QUEUE_URL: str = "https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-onexbots-service-dev-knowledge-queue-dead-letter"
    SQS_DLQ_RETRIEVER_QUEUE_URL: str = "https://sqs.ap-southeast-1.amazonaws.com/156595201415/optiwarehouse-resources-services-dev-retriever-dead-letter-queue"
    SQS_MAX_MESSAGES: int = 10
    SQS_WAIT_TIME: int = 20
    
    # Processing Settings
    MAX_FILE_SIZE_MB: int = 50
    SUPPORTED_FILE_TYPES: str = ".pdf,.txt,.doc,.docx,.url,.html,.md"
    
    # Monitoring Settings
    PROMETHEUS_ENABLED: bool = True
    METRICS_PORT: int = 8003

    @property
    def supported_file_types_list(self) -> List[str]:
        """Convert SUPPORTED_FILE_TYPES string to list."""
        return [ext.strip() for ext in self.SUPPORTED_FILE_TYPES.split(",")]
    print(str(Path(__file__).parent.parent.parent.parent / ".env"))
    model_config = SettingsConfigDict(
        env_file=str(Path(__file__).parent.parent.parent.parent / ".env"),
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore",
        env_nested_delimiter="__",
        validate_default=True
    )

@lru_cache
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()

# Create a singleton settings instance
rag_settings = get_settings() 