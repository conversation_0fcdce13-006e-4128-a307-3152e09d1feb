# Python
__pycache__/
**/__pycache__/
**/__pycache__/**
*.py[cod]
*.pyc
*.pyo
*.pyd
.Python
*.so
*.egg
*.egg-info/
.eggs/
.pytest_cache/
.mypy_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
cover/

# Virtual Environment
.venv/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Environment variables
.env
.env.*

# OS
.DS_Store
Thumbs.db

# 📦 Node.js - Frontend
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
package-lock.json
yarn.lock
.pnpm-lock.yaml