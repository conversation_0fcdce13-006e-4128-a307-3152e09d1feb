from typing import Dict, Any
import httpx
from fastapi import Depends

from onexbots.shared.config import settings


class StaffService:
    def __init__(self):
        self.client = httpx.AsyncClient(
            base_url=settings.ONEXSTAFFS_API_URL,
            headers={"Authorization": f"Bearer {settings.ONEXSTAFFS_API_KEY}"}
        )

    async def get_staff_config(self, staff_id: str) -> Dict[str, Any]:
        """Retrieve staff configuration from ONEXSTAFFS API."""
        try:
            response = await self.client.get(f"/staff/{staff_id}")
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            raise Exception(f"Failed to fetch staff configuration: {str(e)}")

    async def generate_embed_script(self, staff_config: Dict[str, Any]) -> str:
        """Generate embeddable script for staff configuration."""
        # Extract necessary configuration
        config = {
            "staff_id": staff_config.get("id"),
            "api_url": settings.ONEXSTAFFS_API_URL,
            "api_key": settings.ONEXSTAFFS_API_KEY,
            "langchain_config": staff_config.get("langchain_config", {}),
            "tools": staff_config.get("tools", []),
            "mcp_config": staff_config.get("mcp_config", {})
        }

        # Generate embed script
        script = f"""
        <script>
            window.ONEXBOTS_CONFIG = {config};
            (function() {{
                var script = document.createElement('script');
                script.src = '{settings.ONEXSTAFFS_API_URL}/static/onexbots.js';
                script.async = true;
                document.head.appendChild(script);
            }})();
        </script>
        """
        
        return script.strip()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose() 