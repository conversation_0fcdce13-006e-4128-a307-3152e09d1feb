FROM python:3.11

WORKDIR /app

# Create the onexbots directory structure
RUN mkdir -p /app/onexbots

# Copy the required files and directories
COPY __init__.py /app/onexbots/
COPY staff_api /app/onexbots/staff_api
COPY shared /app/onexbots/shared

# Copy pyproject.toml and install dependencies
WORKDIR /app/onexbots/staff_api
RUN pip install --no-cache-dir .

# Set PYTHONPATH to include the project root
ENV PYTHONPATH=/app

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]