"""A tool for interacting with products through the ProductService."""

from typing import Optional
from langchain_core.tools import tool
import logging
import json
from onexbots.shared.services.product_service import ProductService
from ..registry import ToolMetadata, tool_registry
from langchain_core.runnables import RunnableConfig

# Configure logger
logger = logging.getLogger(__name__)


@tool
def products(
    query: Optional[str] = None,
    config: RunnableConfig = None,
) -> str:
    """List products with the given parameters. Action must be 'list'."""
    try:
        page = 0
        limit = 10
        company_id = config.get("configurable", {}).get("company_id")
        if not company_id:
            return "No Product"

        query_params = {"page": page, "limit": limit}
        if query:
            query_params["query"] = json.loads(
                json.dumps(query, indent=2, ensure_ascii=False),
            )
        product_service = ProductService(company_id=company_id)

        response = product_service.list(**query_params)
        if not response:
            return "No Product"
        # Filter products to only include name, price, and image
        filtered_products = [
            {k: item.get(k) for k in ("name", "prices", "images")}
            for item in response.get("items", [])
        ]
        return (
            f"Products: {json.dumps(filtered_products, indent=2, ensure_ascii=False)}"
        )

    except Exception as e:
        logger.error(f"Error in product tool: {str(e)}")
        return "No Product"


# Register the tool with metadata
product_tool_metadata = ToolMetadata(
    name="product",
    description="Useful for searching and listing products with pagination support",
    tags=["products", "product", "list products"],
)
tool_registry.register_tool(products, product_tool_metadata)
logger.debug("Registered product tool")
