from typing import List
from functools import lru_cache
import os
from pathlib import Path
from pydantic_settings import BaseSettings, SettingsConfigDict

# Get the root directory path
ROOT_DIR = Path(__file__).parent.parent.parent.parent

class Settings(BaseSettings):
    """Staff API specific settings."""
    # Override project name for staff API
    PROJECT_NAME: str = "ONEXBOTS Staff API"
   
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True

    model_config = SettingsConfigDict(
        env_file=str(ROOT_DIR / ".env"),
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="allow",
        env_nested_delimiter="__",
        validate_default=True
    )

@lru_cache
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()


# Create a singleton settings instance
staff_settings = get_settings()
