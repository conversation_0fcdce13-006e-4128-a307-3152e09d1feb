from fastapi import APIRouter, Depends, HTTPException, status, Path, Body
from pydantic import BaseModel, Field, validator
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from onexbots.shared.services.virtual_staff_service import Role, Tone, ResponseLength, Language, Temper
from onexbots.shared.models.api import ErrorResponse
from onexbots.shared.config import settings
from app.services.agent.staff_agent import StaffAgent
from onexbots.shared.services.conversation_service import ConversationService
from app.services.mcp.config import MCPConfig as MCPAdapterConfig

router = APIRouter(
    prefix="/config",
    tags=["config"],
    responses={
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"model": ErrorResponse}
    }
)

class LLMProvider(str, Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    AZURE = "azure"

class LLMModel(BaseModel):
    """LLM model configuration."""
    provider: LLMProvider = Field(..., description="LLM provider")
    model_name: str = Field(..., description="Model name")
    custom_url: Optional[str] = Field(None, description="Custom API URL if needed")

class LLMSettings(BaseModel):
    """LLM settings configuration."""
    llm_api_key: str = Field(..., description="Provider API key")
    llm_model: LLMModel = Field(..., description="Model configuration")
    default_llm_temperature: float = Field(0.7, ge=0.0, le=1.0, description="Temperature for response generation")
    max_tokens: int = Field(2000, gt=0, description="Maximum tokens in response")
    max_llm_call_retries: int = Field(3, gt=0, description="Maximum number of retries for LLM calls")
    other_kwargs: Dict[str, Any] = Field(default_factory=dict, description="Additional LLM parameters")

class PersonalTrait(BaseModel):
    """Personal trait configuration."""
    formality: int = Field(..., ge=0, le=100, description="Formality level (0-100)")
    detailed: int = Field(..., ge=0, le=100, description="Detail level (0-100)")
    creativity: int = Field(..., ge=0, le=100, description="Creativity level (0-100)")

class PersonalityConfig(BaseModel):
    """Personality configuration."""
    tone: str = Field("professional", description="Communication tone")
    language: str = Field("en-US", description="Primary language")
    personal_trait: PersonalTrait = Field(..., description="Personal traits configuration")
    farewell: str = Field(..., description="Closing message")
    ethical_constraints: bool = Field(True, description="Whether to enforce ethical constraints")

class KnowledgeConfig(BaseModel):
    """Knowledge base configuration."""
    enabled: bool = Field(True, description="Whether knowledge base is active")
    knowledge_ids: List[str] = Field(default_factory=list, description="List of accessible knowledge documents")
    domain_expertise_ids: List[str] = Field(default_factory=list, description="List of domain expertise IDs")
    max_context_tokens: int = Field(2000, gt=0, description="Maximum context length")
    min_similarity_score: float = Field(0.7, ge=0.0, le=1.0, description="Minimum relevance score")
    context_template: str = Field(
        "Use the following context to answer the question:\n{context}\n\nQuestion: {question}",
        description="Template for context usage"
    )

class ToolConfig(BaseModel):
    """Base tool configuration."""
    enabled: bool = Field(True, description="Whether the tool is enabled")

class CalculatorConfig(ToolConfig):
    """Calculator tool configuration."""
    allowed_operations: List[str] = Field(
        default=["add", "subtract", "multiply", "divide"],
        description="Allowed mathematical operations"
    )
    max_expression_length: int = Field(100, gt=0, description="Maximum expression length")
    max_number: int = Field(1000000, gt=0, description="Maximum number allowed")

class WeatherConfig(ToolConfig):
    """Weather tool configuration."""
    api_key: str = Field(..., description="Weather API key")
    allowed_locations: List[str] = Field(default_factory=list, description="Allowed locations")
    cache_duration: int = Field(3600, gt=0, description="Cache duration in seconds")

class SearchConfig(ToolConfig):
    """Search tool configuration."""
    api_key: str = Field(..., description="Search API key")
    max_results: int = Field(5, gt=0, description="Maximum number of results")
    safe_search: bool = Field(True, description="Enable safe search")

class ToolsConfig(BaseModel):
    """Tools configuration."""
    enabled: List[str] = Field(default_factory=list, description="List of enabled tools")
    config: Dict[str, Union[CalculatorConfig, WeatherConfig, SearchConfig]] = Field(
        default_factory=dict,
        description="Tool-specific configurations"
    )

class MCPAction(str, Enum):
    BLOCK = "block"
    DELAY = "delay"
    MODIFY = "modify"

class MCPAdapterRule(BaseModel):
    """MCP adapter rule configuration."""
    enabled: bool = Field(True, description="Whether the adapter is enabled")
    action: MCPAction = Field(..., description="Action to take")
    patterns: Optional[List[str]] = Field(None, description="Patterns to match")
    replacement: Optional[str] = Field(None, description="Replacement text for modifications")
    max_messages: Optional[int] = Field(None, gt=0, description="Maximum messages for rate limiting")
    time_window: Optional[int] = Field(None, gt=0, description="Time window for rate limiting")

class MCPConfig(BaseModel):
    """MCP configuration."""
    enabled: bool = Field(True, description="Whether MCP is active")
    server_url: str = Field("http://localhost:8000", description="MCP server URL")
    enabled_adapters: List[str] = Field(
        default=["content_filter", "rate_limiter"],
        description="List of enabled MCP adapters"
    )
    adapters_config: Dict[str, MCPAdapterRule] = Field(
        default_factory=lambda: {
            "content_filter": MCPAdapterRule(
                enabled=True,
                action=MCPAction.BLOCK,
                patterns=["inappropriate_word_1", "inappropriate_word_2"]
            ),
            "rate_limiter": MCPAdapterRule(
                enabled=True,
                action=MCPAction.DELAY,
                max_messages=10,
                time_window=60
            )
        },
        description="Configuration for each adapter"
    )

class ConversationConfig(BaseModel):
    """Conversation configuration."""
    max_history_length: int = Field(10, gt=0, description="Maximum conversation history length")
    context_window: int = Field(5, gt=0, description="Number of messages to consider for context")
    response_timeout: int = Field(30, gt=0, description="Maximum response time in seconds")
    fallback_message: str = Field(
        "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.",
        description="Message to use when processing fails"
    )

class StaffConfiguration(BaseModel):
    """Staff configuration model."""
    instruction: str = Field(..., description="Base instruction for the staff member")
    llm_settings: LLMSettings = Field(..., description="LLM settings")
    personality: PersonalityConfig = Field(..., description="Personality configuration")
    knowledge_base: KnowledgeConfig = Field(..., description="Knowledge base configuration")
    tools: ToolsConfig = Field(..., description="Tools configuration")
    mcp: MCPConfig = Field(..., description="MCP configuration")
    conversation: ConversationConfig = Field(..., description="Conversation configuration")

class StaffConfig(BaseModel):
    """Complete staff configuration model."""
    name: str = Field(..., description="Staff member's name")
    department_id: str = Field(..., description="Department ID")
    image: str = Field(..., description="Profile image path")
    role: str = Field(..., description="Staff member's role")
    skills: List[str] = Field(..., description="List of skills")
    greeting: str = Field(..., description="Initial greeting message")
    domain_expertise: List[str] = Field(..., description="List of domain expertise areas")
    configuration: StaffConfiguration = Field(..., description="Staff configuration")

    @validator("configuration")
    def validate_configuration(cls, v):
        """Validate configuration."""
        # Validate tool configurations
        for tool_name in v.tools.enabled:
            if tool_name not in v.tools.config:
                raise ValueError(f"Configuration missing for enabled tool: {tool_name}")
        return v

class TestConfigRequest(BaseModel):
    """Request model for testing staff configuration."""
    staff_id: str = Field(..., description="Staff member ID")
    message: str = Field(..., description="Test message to process")
    user_id: Optional[str] = Field(None, description="User ID for testing")

class TestConfigResponse(BaseModel):
    """Response model for configuration test results."""
    prompt: str = Field(..., description="Generated system prompt based on configuration")

class MessageRole(str, Enum):
    USER = "user"
    VIRTUAL_STAFF = "virtual_staff"
    EXTERNAL_USER = "external_user"

class ConversationResponse(BaseModel):
    """Model for conversation response."""
    success: bool
    message: str
    data: Dict[str, Any]

class TestChatRequest(BaseModel):
    """Request model for testing chat with configuration."""
    content: str = Field(..., description="Message content")
    external_user_id: str = Field(..., description="External user ID")
    connection_id: str = Field(..., description="Connection ID")
    customer_name: str = Field(..., description="Customer's name")
    customer_phone_number: str = Field(..., description="Customer's phone number")
    assignee_id: str = Field(..., description="Bot ID (assignee)")

class TestChatResponse(BaseModel):
    """Response model for chat test results."""
    conversation_id: str = Field(..., description="ID of the test conversation")
    messages: List[Dict[str, Any]] = Field(..., description="List of messages including both user message and AI response")
    conversation_data: Dict[str, Any] = Field(..., description="Additional conversation data")

# Hardcoded test configuration
TEST_CONFIG = {
    "company_id": "test_company",
    "id": "test_staff_1",
    "name": "Alex",
    "department_id": "dept123",
    "image": "alex.jpg",
    "role": Role.CUSTOMER_SUPPORT_AGENT.value,
    "skills": ["Professional", "Friendly", "Patient", "Problem-solver"],
    "greeting": "Hello! I'm Alex, your customer support specialist. How can I help you today?",
    "domain_expertise": ["customer-service", "product-knowledge"],
    "configuration": {
        "instruction": "You are a helpful customer support agent",
        "llm_settings": {
            "llm_api_key": "test-api-key",
            "llm_model": {
                "provider": "openai",
                "model_name": "gpt-4-turbo-preview",
                "custom_url": None
            },
            "default_llm_temperature": 0.7,
            "max_tokens": 2000,
            "max_llm_call_retries": 3,
            "other_kwargs": {}
        },
        "personality": {
            "tone": Tone.PROFESSIONAL.value,
            "language": Language.VI.value,
            "response_length": ResponseLength.MEDIUM.value,
            "temp": Temper.MEDIUM.value,
            "farewell": "Thank you for chatting with me. Have a great day!"
        },
        "knowledge_base": {
            "enabled": True,
            "knowledge_ids": ["product-guide", "faq", "pricing", "support-policy"],
            "domain_expertise_ids": ["customer-service", "product-knowledge"],
            "max_context_tokens": 2000,
            "min_similarity_score": 0.7,
            "context_template": "Use the following context to answer the question:\n{context}\n\nQuestion: {question}"
        },
        "tools": {
            "enabled": ["calculator", "weather", "search"],
            "config": {
                "calculator": {
                    "enabled": True,
                    "allowed_operations": ["add", "subtract", "multiply", "divide"],
                    "max_expression_length": 100,
                    "max_number": 1000000
                },
                "weather": {
                    "enabled": True,
                    "api_key": "test-weather-api-key",
                    "allowed_locations": ["US", "CA", "UK"],
                    "cache_duration": 3600
                },
                "search": {
                    "enabled": True,
                    "api_key": "test-search-api-key",
                    "max_results": 5,
                    "safe_search": True
                }
            }
        },
        "mcp": {
            "enabled": True,
            "server_url": "http://localhost:8000",
            "enabled_adapters": ["content_filter", "rate_limiter"],
            "adapters_config": {
                "content_filter": {
                    "enabled": True,
                    "action": "block",
                    "patterns": ["inappropriate_word_1", "inappropriate_word_2"]
                },
                "rate_limiter": {
                    "enabled": True,
                    "action": "delay",
                    "max_messages": 10,
                    "time_window": 60
                }
            }
        },
        "conversation": {
            "max_history_length": 10,
            "context_window": 5,
            "response_timeout": 30,
            "fallback_message": "I apologize, but I'm having trouble processing your request right now. Please try again in a moment."
        }
    }
}

@router.post(
    "/test",
    response_model=TestConfigResponse,
    status_code=status.HTTP_200_OK,
    summary="Test staff configuration",
    description="Test staff configuration and return generated prompt",
    responses={
        status.HTTP_200_OK: {
            "description": "Generated system prompt",
            "model": TestConfigResponse
        }
    }
)
async def test_staff_config(
    request: TestConfigRequest = Body(..., description="Test configuration request"),
):
    """
    Test staff configuration and return the generated system prompt.
    """
    try:
        # Use hardcoded test configuration
        staff_config = TEST_CONFIG
        
        # Generate system prompt based on language
        from app.services.prompts.vi_prompt import VietnameseSystemPrompt
        from app.services.prompts.en_prompt import EnglishSystemPrompt
        from app.services.prompts.zh_prompt import ChineseSystemPrompt
        from app.services.prompts.ja_prompt import JapaneseSystemPrompt

        language = staff_config["configuration"]["personality"]["language"]
        prompt_generator = {
            "vi": VietnameseSystemPrompt,
            "en": EnglishSystemPrompt,
            "zh": ChineseSystemPrompt,
            "ja": JapaneseSystemPrompt
        }.get(language, EnglishSystemPrompt)

        prompt_instance = prompt_generator(staff=staff_config)
        generated_prompt = prompt_instance.build()
        
        return TestConfigResponse(prompt=generated_prompt)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating prompt: {str(e)}"
        )

@router.post(
    "/test-chat",
    response_model=TestChatResponse,
    status_code=status.HTTP_200_OK,
    summary="Test chat with configuration",
    description="Test chat functionality using the test configuration",
    responses={
        status.HTTP_200_OK: {
            "description": "Chat test results",
            "model": TestChatResponse
        }
    }
)
async def test_chat(
    request: TestChatRequest = Body(..., description="Test chat request"),
):
    """
    Test chat functionality using the test configuration.

    This endpoint allows testing:
    - Chat message processing
    - Response generation
    - Conversation flow
    """
    try:
        # Use hardcoded test configuration
        staff_config = TEST_CONFIG
        
        # Initialize services
        conversation_service = ConversationService()
        staff_agent = StaffAgent(staff_config)
        
        # Process chat using staff agent
        result = await staff_agent.process_chat(
            message=request.content,
            staff_id=request.assignee_id,
            connection_id=request.connection_id,
            external_user_id=request.external_user_id,
            name=request.customer_name,
            phone_number=request.customer_phone_number
        )
        
        return TestChatResponse(
            conversation_id=str(result["conversation_id"]),
            messages=[
                {
                    "content": request.content,
                    "role": "external_user",
                    "connection_id": request.connection_id,
                    "external_user_id": request.external_user_id
                },
                {
                    "content": result["response"],
                    "role": "virtual_staff",
                    "connection_id": request.connection_id,
                    "staff_name": staff_config["name"],
                    "staff_role": staff_config["role"]
                }
            ],
            conversation_data=result["conversation"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error testing chat: {str(e)}"
        )
    