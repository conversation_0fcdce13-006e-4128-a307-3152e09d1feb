from abc import ABC, abstractmethod
from typing import Any
import PyPDF2
import docx
import requests
from bs4 import BeautifulSoup
import io

class FileHandler(ABC):
    @abstractmethod
    async def process(self, content: bytes) -> str:
        pass

class PDFHandler(FileHandler):
    async def process(self, content: bytes) -> str:
        pdf_file = io.BytesIO(content)
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text() + "\n"
        return text

class DocxHandler(FileHandler):
    async def process(self, content: bytes) -> str:
        docx_file = io.BytesIO(content)
        doc = docx.Document(docx_file)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text

class TextHandler(FileHandler):
    async def process(self, content: bytes) -> str:
        return content.decode('utf-8')

class HTMLHandler(FileHandler):
    async def process(self, content: bytes) -> str:
        html_content = content.decode('utf-8')
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
            
        # Get text
        text = soup.get_text()
        
        # Break into lines and remove leading and trailing space
        lines = (line.strip() for line in text.splitlines())
        # Break multi-headlines into a line each
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        # Drop blank lines
        text = '\n'.join(chunk for chunk in chunks if chunk)
        
        return text

class URLHandler(FileHandler):
    async def process(self, content: bytes) -> str:
        url = content.decode('utf-8').strip()
        response = requests.get(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
            
        # Get text
        text = soup.get_text()
        
        # Break into lines and remove leading and trailing space
        lines = (line.strip() for line in text.splitlines())
        # Break multi-headlines into a line each
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        # Drop blank lines
        text = '\n'.join(chunk for chunk in chunks if chunk)
        
        return text 