import re

# Refusal/uncertainty patterns by language (all patterns are lowercase)
REFUSAL_PATTERNS = {
    "en": [
        r"i don't know",
        r"i am not sure",
        r"i do not have enough information",
        r"i cannot answer",
        r"sorry, i don't know",
        r"i apologize, but",
        r"i'm sorry, but i don't have enough information",
        r"i'm sorry, i don't know",
        r"unfortunately, i don't have",
        r"i don't have enough data",
    ],
    "vi": [
        r"tôi không biết",
        r"em không biết",
        r"tôi không chắc",
        r"em không chắc",
        r"không biết",
        r"em không có đủ thông tin",
        r"em không thể trả lời",
        r"xin lỗi, em không biết",
        r"xin lỗi, em không thể trả lời",
        r"em xin lỗi, hiện tại em chưa có đủ thông tin",
        r"em xin lỗi, em chưa có đủ thông tin",
        r"em chưa có đủ thông tin",
        r"xin lỗi",
    ],
    "ja": [
        r"わかりません",
        r"知りません",
        r"情報が足りません",
        r"お答えできません",
        r"申し訳ありませんが",
        r"十分な情報がありません",
        r"答えできません",
    ],
    "zh": [
        r"我不知道",
        r"我不确定",
        r"我没有足够的信息",
        r"无法回答",
        r"对不起，我不知道",
        r"对不起，我无法回答",
        r"很抱歉，我无法回答",
        r"很抱歉，我不知道",
    ],
}


def check_message_resolution(message: str, lang: str = None) -> bool:
    """
    Check if the AI message indicates it does not know the answer.
    If lang is specified, only check that language. Otherwise, check all supported languages.
    Both the message and the patterns are compared in lowercase for robust matching.
    """
    if not message:
        return False
    message_lower = message.lower()
    if lang and lang in REFUSAL_PATTERNS:
        patterns = REFUSAL_PATTERNS[lang]
    else:
        # Flatten all patterns
        patterns = [p for plist in REFUSAL_PATTERNS.values() for p in plist]
    for pattern in patterns:
        if re.search(pattern, message_lower):
            return False
    return True
