services:
    staff_api:
        build:
            context: .
            dockerfile: staff_api/Dockerfile
        ports:
            - "8000:8000"
        env_file:
            - .env
        depends_on:
            - postgres
        networks:
            - onex_network

    rag_pipeline:
        build:
            context: .
            dockerfile: rag_pipeline/Dockerfile
        ports:
            - "7000:8000"
        env_file:
            - .env
        depends_on:
            - postgres
        networks:
            - onex_network

    postgres:
        build:
            context: .
            dockerfile: postgres/Dockerfile
        platform: linux/amd64
        environment:
            - POSTGRES_USER=onexbots
            - POSTGRES_PASSWORD=onexbots
            - POSTGRES_DB=onexbots
        ports:
            - "54321:5432"
        volumes:
            - postgres_data:/var/lib/postgresql/data
        networks:
            - onex_network

volumes:
    postgres_data:

networks:
    onex_network:
        external: true
