from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from prometheus_client import make_asgi_app
from contextlib import asynccontextmanager
import asyncio
import logging
from typing import Optional

from .core.config import rag_settings
from onexbots.shared.config import settings
from app.core.middleware import RequestLoggingMiddleware
from app.api.routes import router as api_router
from app.services.message_consumer import MessageConsumer
from app.services.file_processor import FileProcessor
from app.services.json_processor import JsonProcessor
from onexbots.shared.services.embedding_service import EmbeddingService
from onexbots.shared.services.knowledge_service import KnowledgeService

# Initialize logging
logger = logging.getLogger(__name__)

# Global task management
message_processing_task: Optional[asyncio.Task] = None
retriever_processing_task: Optional[asyncio.Task] = None
shutdown_event = asyncio.Event()
message_consumer = MessageConsumer()
file_processor = FileProcessor()
json_processor = JsonProcessor()
embedding_service = EmbeddingService(settings)
knowledge_service = KnowledgeService()


async def process_single_message(message: dict) -> bool:
    """Process a single message with proper error handling."""
    try:
        # Process S3 event
        s3_events = await message_consumer.process_message(message)
        if not s3_events:
            return False

        for s3_event in s3_events:
            try:
                # Download file from S3
                file_path = await file_processor.download_from_s3(
                    s3_event["bucket"], s3_event["key"]
                )
                if not file_path:
                    await message_consumer.move_to_dlq(message)
                    continue

                # Process file
                processed_file = await file_processor.process_file(file_path)
                if not processed_file:
                    await message_consumer.move_to_dlq(message)
                    continue

                # Extract knowledge_id from S3 key
                parts = s3_event["key"].split("/")
                if len(parts) < 2:
                    logger.error(f"Invalid S3 key format: {s3_event['key']}")
                    await message_consumer.move_to_dlq(message)
                    continue

                knowledge_id = s3_event["knowledge_id"]
                company_id = s3_event["company_id"]

                # Add or update knowledge document
                knowledge_doc = (
                    await embedding_service.add_or_update_knowledge_document(
                        knowledge_id=knowledge_id,
                        content=processed_file["content"],
                        metadata=processed_file["metadata"],
                        source="s3",  # or any other source identifier you prefer
                    )
                )

                if not knowledge_doc:
                    logger.error(
                        f"Failed to process knowledge document: {knowledge_id}"
                    )
                    await message_consumer.move_to_dlq(message)
                    continue

                logger.info(
                    f"Successfully processed knowledge document: {knowledge_id}"
                )
                # Update knowledge status to processing
                result = knowledge_service.update_status(
                    id=knowledge_id, status="READY", company_id=company_id
                )

                if not result:
                    logger.error(
                        f"Failed to update knowledge status for {s3_event['key']}"
                    )
                    continue

            except Exception as e:
                logger.error(f"Error processing S3 event: {str(e)}")
                await message_consumer.move_to_dlq(message)
                continue

        # Delete the message from the queue after successful processing
        try:
            await message_consumer.delete_message(message)
            logger.info("Successfully deleted processed message from queue")
        except Exception as e:
            logger.error(f"Failed to delete message from queue: {str(e)}")
            return False

        return True

    except Exception as e:
        logger.error(f"Error processing message: {str(e)}")
        return False


async def process_retriever_message(message):
    """Process a single message with proper error handling."""
    try:
        # Process SQS event
        sqs_event = await message_consumer.process_sqs_message(message)
        if not sqs_event:
            return False

        try:
            event_name = sqs_event['event_name']
            table_name = sqs_event['table_name']
            record_data = sqs_event['record_data']
            if table_name != "product":
                await message_consumer.move_to_retriever_dlq(message)
                logger.error(f"Invalid table name: {table_name}")
                return False
            
            if event_name in ["INSERT", "MODIFY"]:
                # Process product json
                processed_product = await json_processor.process_json(record_data, table_name)
                if not processed_product:
                    await message_consumer.move_to_retriever_dlq(message)
                    return False
                # Add or update knowledge document
                knowledge_doc = await embedding_service.add_or_update_record_document(processed_record=processed_product)
            elif event_name == "REMOVE":
                knowledge_doc = await embedding_service.delete_record_document(record_id=processed_product["metadata"]["id"])

            if not knowledge_doc:
                logger.error(
                    f"Failed to process knowledge document: {processed_product['metadata']['id']}"
                )
                await message_consumer.move_to_retriever_dlq(message)
                return False

            logger.info(
                f"Successfully processed knowledge document: {processed_product['metadata']['id']}"
            )
        except Exception as e:
            logger.error(f"Error processing SQS event: {str(e)}")
            await message_consumer.move_to_retriever_dlq(message)
            return False

        # Delete the message from the queue after successful processing
        try:
            await message_consumer.delete_message(message, queue_url=rag_settings.SQS_RETRIEVER_QUEUE_URL)
            logger.info("Successfully deleted processed message from queue")
        except Exception as e:
            logger.error(f"Failed to delete message from queue: {str(e)}")
            return False

        return True

    except Exception as e:
        logger.error(f"Error processing message: {str(e)}")
        return False


async def process_messages(queue_url: str):
    retry_count = 0
    max_retries = 5
    base_delay = 1  # seconds
    while not shutdown_event.is_set():
        try:
            response = message_consumer.sqs.receive_message(
                QueueUrl=queue_url,
                MaxNumberOfMessages=rag_settings.SQS_MAX_MESSAGES,
                WaitTimeSeconds=rag_settings.SQS_WAIT_TIME,
            )
            if "Messages" in response:
                tasks = []
                for message in response["Messages"]:
                    # Separate logic for different queues
                    if queue_url == rag_settings.SQS_QUEUE_URL:
                        tasks.append(process_single_message(message))
                    elif queue_url == rag_settings.SQS_RETRIEVER_QUEUE_URL:
                        tasks.append(process_retriever_message(message))
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)
                retry_count = 0
            else:
                await asyncio.sleep(rag_settings.SQS_WAIT_TIME)
        except Exception as e:
            logger.error(f"[{queue_url}] Error in message processing: {str(e)}")
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"[{queue_url}] Max retries reached, shutting down")
                break
            delay = base_delay * (2**retry_count)
            logger.info(f"[{queue_url}] Retrying in {delay} seconds...")
            await asyncio.sleep(delay)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    global message_processing_task, retriever_processing_task
    try:
        message_processing_task = asyncio.create_task(
            process_messages(rag_settings.SQS_QUEUE_URL)
        )
        retriever_processing_task = asyncio.create_task(
            process_messages(rag_settings.SQS_RETRIEVER_QUEUE_URL)
        )
        yield
    finally:
        # Shutdown
        shutdown_event.set()
        for task in [message_processing_task, retriever_processing_task]:
            if task:
                try:
                    await asyncio.wait_for(task, timeout=5.0)
                except asyncio.TimeoutError:
                    logger.warning("A message processing task did not complete in time")
                except Exception as e:
                    logger.error(f"Error during message processing shutdown: {str(e)}")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title=rag_settings.PROJECT_NAME,
        description=rag_settings.PROJECT_DESCRIPTION,
        version=rag_settings.VERSION,
        docs_url=None,
        redoc_url=None,
        lifespan=lifespan,
    )

    # Custom Swagger UI
    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        return get_swagger_ui_html(
            openapi_url="/openapi.json",
            title=f"{app.title} - Swagger UI",
            swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
            swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
        )

    # Custom OpenAPI schema
    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema
        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )
        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # Middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=rag_settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_middleware(RequestLoggingMiddleware)

    # Routes
    app.include_router(api_router, prefix=rag_settings.API_V1_STR)

    # Prometheus metrics
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)

    # Root endpoint
    @app.get("/", include_in_schema=False)
    async def root():
        return {
            "message": rag_settings.PROJECT_NAME,
            "docs_url": "/docs",
            "redoc_url": "/redoc",
            "openapi_url": "/openapi.json",
        }

    return app


app = create_app()

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
    )
