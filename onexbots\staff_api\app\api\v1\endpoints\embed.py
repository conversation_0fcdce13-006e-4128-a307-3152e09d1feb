from fastapi import APIRouter, Depends, HTTPException, status, Path
from pydantic import Field

from onexbots.shared import StaffService
from onexbots.shared.models.api import EmbedResponse, ErrorResponse
from onexbots.shared.config import settings


router = APIRouter(
    prefix="/embed",
    tags=["embed"],
    responses={
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"model": ErrorResponse}
    }
)


@router.get(
    "/{staff_id}",
    response_model=EmbedResponse,
    status_code=status.HTTP_200_OK,
    summary="Get embed script",
    description="Generates an embeddable script for integrating staff chat on external websites",
    responses={
        status.HTTP_200_OK: {
            "description": "Successful script generation",
            "model": EmbedResponse
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Staff member not found",
            "model": ErrorResponse
        }
    }
)
async def get_embed_script(
    staff_id: str = Path(..., description="Unique identifier of the staff member"),
    staff_service: StaffService = Depends(StaffService),
):
    """
    Generate an embeddable script for staff chat integration.

    This endpoint generates a JavaScript snippet that can be embedded on external websites
    to provide chat functionality with the specified staff member.

    The script includes:
    - Staff configuration
    - API endpoints
    - Authentication details
    - UI customization options
    """
    try:
        # Get staff configuration
        staff_config = await staff_service.get_staff_config(staff_id)
        
        # Generate embed script
        embed_script = await staff_service.generate_embed_script(staff_config)
        
        return EmbedResponse(script=embed_script)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating embed script: {str(e)}"
        ) 