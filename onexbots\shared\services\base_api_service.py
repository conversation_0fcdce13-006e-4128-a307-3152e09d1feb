import requests
import logging
from typing import Optional, Dict, Any, TypeVar, Generic, List
from abc import ABC, abstractmethod
from ..config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

T = TypeVar("T")


class BaseAPIService(ABC, Generic[T]):
    """Base class for API services with common functionality."""

    def __init__(
        self,
        api_url: str = None,
        cognito_client_id: str = None,
        cognito_client_secret: str = None,
        cognito_token_url: str = None,
        cognito_scope: str = None,
    ):
        self.api_url = (api_url or settings.ONEXBOTS_SERVICES_API_URL).rstrip("/")
        self.cognito_client_id = cognito_client_id or settings.COGNITO_CLIENT_ID
        self.cognito_client_secret = (
            cognito_client_secret or settings.COGNITO_CLIENT_SECRET
        )
        self.cognito_token_url = cognito_token_url or settings.COGNITO_TOKEN_URL
        self.cognito_scope = cognito_scope or settings.COGNITO_SCOPE
        self.access_token = None

    def get_access_token(self) -> Optional[str]:
        """Get access token from Cognito."""
        try:
            response = requests.post(
                self.cognito_token_url,
                data={
                    "grant_type": "client_credentials",
                    "client_id": self.cognito_client_id,
                    "client_secret": self.cognito_client_secret,
                    "scope": self.cognito_scope,
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"},
            )
            response.raise_for_status()
            self.access_token = response.json()["access_token"]
            return self.access_token
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get access token: {str(e)}")
            if hasattr(e.response, "text"):
                logger.error(f"Response: {e.response.text}")
            return None

    def _get_headers(self) -> Dict[str, str]:
        """Get headers with authorization token."""
        if not self.access_token:
            self.get_access_token()
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

    def _make_request(
        self,
        method: str,
        endpoint: str,
        service: Optional[str] = None,
        company_id: Optional[str] = None,
        **kwargs,
    ) -> Optional[Dict[str, Any]]:
        """Make an HTTP request to the API.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            company_id: Optional company ID for company-specific endpoints
            **kwargs: Additional arguments for requests.request
        """
        try:
            logger.info(f"Make request :{endpoint}")
            # Ensure endpoint starts with a slash
            if not endpoint.startswith("/"):
                endpoint = "/" + endpoint

            # Construct URL with proper path handling
            url = f"{self.api_url.rstrip('/')}"
            if company_id:
                url = f"{url}/{service}/services/{company_id}"
            url = f"{url}{endpoint}"

            response = requests.request(
                method=method, url=url, headers=self._get_headers(), **kwargs
            )

            # If unauthorized, try to refresh token and retry once
            if response.status_code == 401:
                logger.info(
                    "Received 401 Unauthorized, attempting to refresh token and retry"
                )
                self.access_token = None  # Force token refresh
                response = requests.request(
                    method=method, url=url, headers=self._get_headers(), **kwargs
                )

            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to make {method} request to {endpoint}: {str(e)}")
            if hasattr(e.response, "text"):
                logger.error(f"Response: {e.response.text}")
            return None

    @abstractmethod
    def create(self, data: Dict[str, Any]) -> Optional[T]:
        """Create a new resource."""
        pass

    @abstractmethod
    def get(self, id: str) -> Optional[T]:
        """Get a resource by ID."""
        pass

    @abstractmethod
    def list(self) -> Optional[List[T]]:
        """List all resources."""
        pass

    @abstractmethod
    def delete(self, id: str) -> bool:
        """Delete a resource by ID."""
        pass
