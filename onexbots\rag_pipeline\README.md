# RAG Pipeline Service

A FastAPI-based service for processing documents and generating embeddings using RAG (Retrieval-Augmented Generation) pipeline.

## Features

- Document processing (PDF, TXT, DOCX, XLSX)
- Embedding generation using OpenAI
- Vector storage in PostgreSQL
- S3 integration for file storage
- SQS for message processing
- Prometheus metrics

## Prerequisites

- Python 3.9+
- PostgreSQL 17+ with pgvector extension
- AWS credentials for S3 and SQS
- OpenAI API key

## Installation

1. Clone the repository:
```bash
git clone https://github.com/your-org/onexbots.git
cd onexbots/rag_pipeline
```

2. Create and activate virtual environment:
```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

## Configuration

The service can be configured through environment variables:

### Database Settings
- `POSTGRES_USER`: PostgreSQL username
- `POSTGRES_PASSWORD`: PostgreSQL password
- `POSTGRES_HOST`: PostgreSQL host
- `POSTGRES_PORT`: PostgreSQL port
- `POSTGRES_DB`: PostgreSQL database name

### AWS Settings
- `AWS_ACCESS_KEY_ID`: AWS access key
- `AWS_SECRET_ACCESS_KEY`: AWS secret key
- `AWS_REGION`: AWS region
- `S3_BUCKET_NAME`: S3 bucket name
- `SQS_QUEUE_URL`: SQS queue URL

### OpenAI Settings
- `OPENAI_API_KEY`: OpenAI API key
- `EMBEDDING_MODEL`: OpenAI embedding model name

### Service Settings
- `UPLOAD_DIR`: Local upload directory
- `MAX_FILE_SIZE`: Maximum file size in bytes
- `ALLOWED_EXTENSIONS`: Comma-separated list of allowed file extensions

## Running the Service

### Development
```bash
./run.sh
```

### Production
```bash
docker build -t onexbots-rag-pipeline .
docker run -d --name rag-pipeline -p 8002:8002 onexbots-rag-pipeline
```

## API Documentation

Once the service is running, you can access the API documentation at:
- Swagger UI: http://localhost:8002/docs
- ReDoc: http://localhost:8002/redoc

### Endpoints

#### Upload File
```http
POST /api/v1/upload
Content-Type: multipart/form-data

file: <file>
```

#### Search Similar
```http
POST /api/v1/search
Content-Type: application/json

{
    "query": "search text",
    "limit": 5
}
```

#### Health Check
```http
GET /api/v1/health
```

## Development

### Code Style
The project uses:
- Black for code formatting
- isort for import sorting
- flake8 for linting
- mypy for type checking

Run code quality checks:
```bash
pre-commit run --all-files
```

### Testing
Run tests:
```bash
pytest
```

## Monitoring

The service exposes Prometheus metrics at `/metrics`. Key metrics include:
- `rag_pipeline_processed_files_total`: Total files processed
- `rag_pipeline_processing_time_seconds`: Processing time histogram

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details. 