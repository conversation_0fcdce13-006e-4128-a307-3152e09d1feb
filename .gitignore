# Python cache files
__pycache__/
**/__pycache__/
*.py[cod]
*$py.class
*.pyc
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/

cover/

# Virtual environments
.env
.venv/
venv/
ENV/
env/
.env.*
!.env.example
*.env

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
*.log
logs/
log/

# Distribution / packaging
dist/
build/
*.egg-info/
*.egg

# Local development
*.sqlite3
*.db

# Python cache files
__pycache__/
*.py[cod]
*.class
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.coverage.*
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 📦 Node.js - Frontend
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
package-lock.json
yarn.lock
.pnpm-lock.yaml