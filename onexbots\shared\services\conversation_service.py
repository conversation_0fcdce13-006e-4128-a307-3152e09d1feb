import logging
from typing import Dict, Any, Optional, List
import httpx
from uuid import UUID, uuid4
from pathlib import Path
from enum import Enum
from pydantic import BaseModel
from datetime import datetime

from onexbots.shared.config import settings
from .base_api_service import BaseAPIService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MessageRole(str, Enum):
    """Valid message roles."""

    USER = "USER"
    VIRTUAL_STAFF = "VIRTUAL_STAFF"
    EXTERNAL_USER = "EXTERNAL_USER"


class Message:

    def __init__(
        self,
        id: Optional[str] = None,
        content: str = None,
        role: MessageRole = None,
        connection_id: str = None,
        user_id: Optional[str] = None,
        external_user_id: Optional[str] = None,
        is_resolved: Optional[bool] = True,
        staff_id: Optional[str] = None,
        response_message_id: Optional[str] = None,
        created_at: Optional[str] = None,
        resolved_time: Optional[int] = None,
    ):
        self.id = id
        self.content = content
        self.role = role
        self.connection_id = connection_id
        self.user_id = user_id
        self.external_user_id = external_user_id
        self.is_resolved = is_resolved
        self.staff_id = staff_id
        self.response_message_id = response_message_id
        self.created_at = created_at
        self.resolved_time = resolved_time


class Conversation:
    def __init__(
        self,
        name: str,
        customer_id: str,
        image: Optional[str],
        members: List[str],
        files: Optional[List[str]],
        links: Optional[List[str]],
        assignee_id: str,
        connection_id: Optional[str],
        role=MessageRole,
    ):
        self.name = name
        self.cusomer_id = customer_id
        self.image = image
        self.members = members
        self.files = files
        self.link = links
        self.assignee_id = assignee_id
        self.connection_id = connection_id
        self.role = role


class CreateConversationRequest(BaseModel):
    customer_name: str
    customer_phone_number: str
    assignee_id: str
    staff_id: str
    name: Optional[str] = None
    user_id: Optional[str] = None
    connection_id: Optional[str] = None
    external_user_id: Optional[str] = None
    image: Optional[str] = None
    role: MessageRole = MessageRole.VIRTUAL_STAFF.value
    channel_id: Optional[str] = None


class ConversationService(BaseAPIService[Dict[str, Any]]):
    """Service for managing conversations and interacting with the Conversation API."""

    def __init__(self):
        super().__init__()
        self.client = httpx.AsyncClient(
            base_url=settings.ONEXSTAFFS_API_URL,
            headers={"Authorization": f"Bearer {settings.ONEXSTAFFS_API_KEY}"},
        )

    def create(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new conversation."""
        return self._make_request("POST", "/conversations", json=data)

    def get(self, id: str) -> Optional[Dict[str, Any]]:
        """Get a conversation by ID."""
        return self._make_request("GET", f"/conversations/{id}")

    def get_messages(self, conversation_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get all messages for a conversation."""
        return self._make_request("GET", f"/conversations/{conversation_id}/messages")

    def list(self) -> Optional[List[Dict[str, Any]]]:
        """List all conversations."""
        return self._make_request("GET", "/conversations")

    def delete(self, id: str) -> bool:
        """Delete a conversation by ID."""
        result = self._make_request("DELETE", f"/conversations/{id}")
        return result is not None

    async def _log_conversation(
        self, conversation_id: str, message: Message, response: Optional[str] = None
    ):
        """Log conversation to ONEXSTAFFS API."""
        try:
            # Get the role and validate it
            role = message.role.value.upper()
            if role not in [r.value for r in MessageRole]:
                logger.warning(f"Invalid role '{role}', defaulting to EXTERNAL_USER")
                role = MessageRole.EXTERNAL_USER.value

            log_data = vars(message).copy()
            log_data["role"] = role
            # Optionally add response if needed
            if response is not None:
                log_data["response"] = response
            # Remove keys with None values
            log_data = {k: v for k, v in log_data.items() if v is not None}
            self._make_request(
                "POST", f"/conversations/{conversation_id}/messages", json=log_data
            )
        except Exception as e:
            # Log error but don't fail the request
            logger.error(f"Failed to log conversation: {str(e)}")

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()

    def create_conversation(
        self,
        req: CreateConversationRequest,
    ) -> Optional[Dict[str, Any]]:
        """Create a new conversation with the specified fields."""
        body = req.dict(exclude_none=True)
        return self._make_request("POST", "/conversations", json=body)
