@echo off
REM Set PYTHONPATH to include the project root
set PYTHONPATH=%PYTHONPATH%;..\..

REM Activate virtual environment if it exists
IF EXIST ".venv" (
    call .venv\Scripts\activate.bat
)

REM Print environment for verification
echo Current Python: %where python%
echo PYTHONPATH: %PYTHONPATH%
echo Python sys.path:
python -c "import sys; [print(p) for p in sys.path]"

REM Run the FastAPI application with debugpy
python -m debugpy --listen 5678 --wait-for-client -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
