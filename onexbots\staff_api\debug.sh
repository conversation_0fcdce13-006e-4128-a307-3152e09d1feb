#!/bin/bash

# Set PYTHONPATH to include the project root
export PYTHONPATH=$PYTHONPATH:../..

# Activate virtual environment if it exists
if [ -d ".venv" ]; then
    source .venv/bin/activate
fi

# Print environment for verification
echo "Current Python: $(which python)"
echo "PYTHONPATH: $PYTHONPATH"
echo "Python sys.path:"
python -c "import sys; [print(p) for p in sys.path]"

# Run the FastAPI application with debugpy
python -m debugpy --listen 5678 --wait-for-client -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload