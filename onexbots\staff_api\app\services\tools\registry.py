"""Tool management system for loading and organizing LangChain tools.

This module provides functionality for organizing and managing LangChain tools
that can be used with LangGraph agents. It handles discovering, registering, and
providing access to tools through a centralized registry.
"""

import importlib
import logging
import os
from typing import Dict, Any, Type, Optional, Union
from dataclasses import dataclass
from langchain_core.tools import BaseTool, StructuredTool
import inspect

# Configure logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


@dataclass
class ToolMetadata:
    """Metadata for a tool."""

    name: str
    description: str
    tags: list[str] = None


class ToolRegistry:
    """Singleton registry for tools."""

    _instance = None
    _registry: Dict[str, Union[Type[BaseTool], StructuredTool]] = {}
    _instances: Dict[str, Union[BaseTool, StructuredTool]] = (
        {}
    )  # Store instantiated tools

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ToolRegistry, cls).__new__(cls)
            logger.debug("Created new ToolRegistry instance")
        return cls._instance

    def __init__(self):
        """Initialize the registry."""
        # Only initialize if this is the first time
        if not hasattr(self, "_initialized"):
            self._initialized = True
            logger.debug("Initialized ToolRegistry")

    @classmethod
    def register_tool(
        cls, tool: Union[Type[BaseTool], StructuredTool], metadata: ToolMetadata
    ) -> None:
        """Register a tool with metadata.

        Args:
            tool: Either a BaseTool class or a StructuredTool instance
            metadata: Tool metadata
        """
        # Register the tool
        if isinstance(tool, StructuredTool):
            # For StructuredTool, store the instance directly
            cls._registry[metadata.name] = tool
            cls._instances[metadata.name] = tool
            logger.info(f"Registered StructuredTool: {metadata.name}")
        else:
            # For BaseTool class, store the class and create instance
            tool.tool_metadata = metadata
            cls._registry[metadata.name] = tool
            try:
                instance = tool()
                cls._instances[metadata.name] = instance
                logger.debug(f"Pre-instantiated BaseTool: {metadata.name}")
            except Exception as e:
                logger.error(f"Error pre-instantiating tool {metadata.name}: {str(e)}")

        logger.debug(f"Current tools in registry: {list(cls._registry.keys())}")

    @classmethod
    def get_tools(cls) -> Dict[str, Union[Type[BaseTool], StructuredTool]]:
        """Get all registered tools."""
        return cls._registry.copy()

    @classmethod
    def get_instances(cls) -> Dict[str, Union[BaseTool, StructuredTool]]:
        """Get all instantiated tools."""
        return cls._instances.copy()

    @classmethod
    def get_instance(cls, tool_name: str) -> Optional[Union[BaseTool, StructuredTool]]:
        """Get an instantiated tool by name."""
        if tool_name in cls._instances:
            return cls._instances[tool_name]

        if tool_name in cls._registry:
            tool = cls._registry[tool_name]
            if isinstance(tool, StructuredTool):
                return tool
            try:
                instance = tool()
                cls._instances[tool_name] = instance
                return instance
            except Exception as e:
                logger.error(f"Error creating tool {tool_name}: {str(e)}")
                return None

        return None

    @classmethod
    def clear(cls):
        """Clear the registry."""
        cls._registry.clear()
        cls._instances.clear()
        logger.debug("Cleared tool registry")


# Create global registry instance
tool_registry = ToolRegistry()


class ToolManager:
    """Manages tool registration, discovery and instantiation."""

    def __init__(self):
        """Initialize the tool manager."""
        logger.debug("Initialized ToolManager")
        # Discover and load tools on initialization
        self.discover_tools()
        self.load_all_tools()

    def discover_tools(self) -> None:
        """Discover and load tools from the tools directory."""
        tools_dir = os.path.dirname(os.path.abspath(__file__))
        logger.debug(f"Discovering tools in directory: {tools_dir}")

        # Get all Python files that don't start with '__'
        tool_files = []
        for item in os.listdir(tools_dir):
            if item.endswith(".py") and not item.startswith("__"):
                tool_files.append(item[:-3])  # Remove .py extension
        logger.debug(f"Found tool files: {tool_files}")

        # Force import tools to ensure registration runs
        for tool_file in tool_files:
            try:
                # Skip registry.py itself
                if tool_file in ["registry", "retriever_tool"]:
                    continue

                # Import using absolute path
                module_path = (
                    f"onexbots.staff_api.app.services.tools.system_tools.{tool_file}"
                )
                logger.debug(f"Attempting to import module: {module_path}")

                # Import the module
                module = importlib.import_module(module_path)
                logger.info(f"Successfully loaded tool module: {tool_file}")

                # Verify tool registration
                found_tools = False
                for name, obj in inspect.getmembers(module):
                    # Check for BaseTool classes
                    if (
                        inspect.isclass(obj)
                        and issubclass(obj, BaseTool)
                        and hasattr(obj, "tool_metadata")
                    ):
                        found_tools = True
                        logger.debug(f"Found registered BaseTool class: {name}")
                        logger.debug(f"Tool metadata: {obj.tool_metadata}")
                    # Check for StructuredTool instances
                    elif isinstance(obj, StructuredTool):
                        found_tools = True
                        logger.debug(f"Found registered StructuredTool: {name}")

                if not found_tools:
                    logger.warning(f"No tools found in module: {tool_file}")

            except Exception as e:
                logger.error(
                    f"Failed to load tool module {tool_file}: {str(e)}", exc_info=True
                )

        # Log final registry state
        registered_tools = list(ToolRegistry.get_tools().keys())
        logger.debug(f"Tools in registry after discovery: {registered_tools}")
        if not registered_tools:
            logger.warning("No tools were registered during discovery!")

    def load_all_tools(self) -> Dict[str, Union[BaseTool, StructuredTool]]:
        """Load all registered tools.

        Returns:
            Dict[str, Union[BaseTool, StructuredTool]]: Dictionary mapping tool names to instantiated tool objects
        """
        registered_tools = list(ToolRegistry.get_tools().keys())
        logger.debug(f"Loading all tools from registry: {registered_tools}")

        if not registered_tools:
            logger.warning("No tools found in registry to load!")
            return {}

        # Instantiate all tools
        for name in registered_tools:
            tool = ToolRegistry.get_instance(name)
            if tool:
                logger.debug(f"Successfully loaded tool: {name}")
            else:
                logger.warning(f"Failed to load tool: {name}")

        loaded_tools = list(ToolRegistry.get_instances().keys())
        logger.debug(f"Loaded tools: {loaded_tools}")
        if not loaded_tools:
            logger.warning("No tools were successfully loaded!")

        return ToolRegistry.get_instances()

    def get_tool(self, tool_name: str) -> Optional[Union[BaseTool, StructuredTool]]:
        """Get a tool instance.

        Args:
            tool_name: The name of the tool to get

        Returns:
            Optional[Union[BaseTool, StructuredTool]]: Tool instance or None if not found
        """
        tool = ToolRegistry.get_instance(tool_name)
        if tool:
            logger.debug(f"Retrieved tool: {tool_name}")
        else:
            logger.warning(f"Tool not found: {tool_name}")
        return tool

    def get_metadata(self) -> Dict[str, ToolMetadata]:
        """Get metadata for all registered tools.

        Returns:
            Dict[str, ToolMetadata]: Dictionary mapping tool names to their metadata
        """
        return {
            name: cls.tool_metadata
            for name, cls in ToolRegistry.get_tools().items()
            if hasattr(cls, "tool_metadata")
        }

    def get_tool_metadata(self, tool_name: str) -> Optional[ToolMetadata]:
        """Get metadata for a specific tool.

        Args:
            tool_name: The name of the tool to get metadata for

        Returns:
            Optional[ToolMetadata]: Tool metadata or None if not found
        """
        tools = ToolRegistry.get_tools()
        if tool_name not in tools:
            return None
        return getattr(tools[tool_name], "tool_metadata", None)

    def get_tools_by_tag(self, tag: str) -> Dict[str, Union[BaseTool, StructuredTool]]:
        """Get tools filtered by tag.

        Args:
            tag: The tag to filter tools by

        Returns:
            Dict[str, Union[BaseTool, StructuredTool]]: Dictionary mapping tool names to tools that have the specified tag
        """
        return {
            name: ToolRegistry.get_instance(name)
            for name, cls in ToolRegistry.get_tools().items()
            if hasattr(cls, "tool_metadata") and tag in cls.tool_metadata.tags
        }
