import logging
from typing import Dict, Any, Type, Optional, List
from pathlib import Path
import importlib
import inspect

from .base import BaseMCPAdapter

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)  # Set logger level to DEBUG

class MCPRegistry:
    """Registry for managing Model Context Protocol rules."""
    
    def __init__(self):
        self.rules: Dict[str, Type[BaseMCPAdapter]] = {}
        logger.debug("Initialized MCPRegistry")
    
    def register_rule(self, rule_type: str, rule_class: Type[BaseMCPAdapter]) -> None:
        """Register a Model Context Protocol rule."""
        if not issubclass(rule_class, BaseMCPAdapter):
            raise ValueError(f"Rule class must inherit from BaseMCPAdapter: {rule_class}")
        
        # Convert rule type to standard format (e.g., "ContentFilter" -> "content_filter")
        rule_type = rule_type.lower()
        if "filter" in rule_type and not rule_type.endswith("_filter"):
            rule_type = rule_type.replace("filter", "_filter")
        if "limiter" in rule_type and not rule_type.endswith("_limiter"):
            rule_type = rule_type.replace("limiter", "_limiter")
        
        # Remove "adapter" suffix if present
        if rule_type.endswith("adapter"):
            rule_type = rule_type[:-7]
        
        self.rules[rule_type] = rule_class
        logger.info(f"Registered MCP rule: {rule_type}")
        logger.debug(f"Current rules in registry: {list(self.rules.keys())}")
    
    def get_rule(self, rule_type: str) -> Optional[Type[BaseMCPAdapter]]:
        """Get a registered rule by type."""
        # Convert rule type to standard format
        rule_type = rule_type.lower()
        if "filter" in rule_type and not rule_type.endswith("_filter"):
            rule_type = rule_type.replace("filter", "_filter")
        if "limiter" in rule_type and not rule_type.endswith("_limiter"):
            rule_type = rule_type.replace("limiter", "_limiter")
            
        # Remove "adapter" suffix if present
        if rule_type.endswith("adapter"):
            rule_type = rule_type[:-7]
            
        logger.debug(f"Looking up rule: {rule_type}")
        logger.debug(f"Available rules: {list(self.rules.keys())}")
        return self.rules.get(rule_type)
    
    def create_rule(self, rule_type: str, config: Dict[str, Any]) -> Optional[BaseMCPAdapter]:
        """Create a rule instance."""
        rule_class = self.get_rule(rule_type)
        if not rule_class:
            logger.warning(f"Rule not found: {rule_type}")
            logger.debug(f"Available rules: {list(self.rules.keys())}")
            return None
        
        try:
            # Ensure config has required fields
            if "enabled" not in config:
                config["enabled"] = True
            
            return rule_class(config)
        except Exception as e:
            logger.error(f"Error creating rule {rule_type}: {str(e)}")
            return None
    
    def load_rules_from_directory(self, directory: str) -> None:
        """Load rules from a directory."""
        try:
            rules_dir = Path(directory)
            if not rules_dir.exists():
                logger.warning(f"Rules directory not found: {directory}")
                return
            
            # Get all Python files in the directory
            rule_files = [f for f in rules_dir.glob("*.py") if not f.name.startswith("__")]
            
            for rule_file in rule_files:
                try:
                    # Import using relative path
                    module_path = f".{rule_file.stem}"
                    module = importlib.import_module(module_path, package="onexbots.staff_api.app.services.mcp")
                    
                    # Find rule classes
                    for name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and 
                            issubclass(obj, BaseMCPAdapter) and 
                            obj != BaseMCPAdapter):
                            # Register the rule
                            self.register_rule(name.lower(), obj)
                            logger.debug(f"Found and registered rule class: {name}")
                            
                except Exception as e:
                    logger.error(f"Error loading rule from {rule_file}: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error loading rules from directory: {str(e)}")
    
    def list_rules(self) -> List[str]:
        """List all registered rules."""
        return list(self.rules.keys())
    
    async def process_message(
        self,
        message: str,
        user_id: str,
        rules_config: List[Dict[str, Any]]
    ) -> str:
        """Process a message through all enabled rules."""
        try:
            processed_message = message
            logger.debug(f"Processing message with {len(rules_config)} rules")
            logger.debug(f"Available rules: {list(self.rules.keys())}")
            
            for rule_config in rules_config:
                rule_type = rule_config.get("type")
                if not rule_type:
                    continue
                
                logger.debug(f"Processing rule: {rule_type}")
                rule = self.create_rule(rule_type, rule_config)
                if rule:
                    processed_message = await rule.process(processed_message, user_id)
            
            return processed_message
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return message 