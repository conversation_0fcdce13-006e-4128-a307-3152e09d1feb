from typing import Optional
import tempfile
import os
import logging
from onexbots.shared.config import settings
from langchain_openai import ChatOpenAI
from langchain_community.document_loaders import (
    UnstructuredHTMLLoader,
)

logger = logging.getLogger(__name__)


class JsonProcessor:
    def __init__(self):
        self.llm = ChatOpenAI(
            model_name="gpt-3.5-turbo",
            temperature=0.7,
            streaming=False,  # Enable streaming by default
            max_tokens=2000,
            api_key=settings.OPENAI_API_KEY
        )
    async def process_json(self, record_data, table_name) -> Optional[list]:
        """Flatten toàn bộ dữ liệu record_data thành string, mỗi level thụt vào 4 space, key chỉ gồm chữ và số. Tiền xử lý các trường đặc biệt như description là HTML."""
        try:
            def normalize_key(key: str) -> str:
                return ''.join(c for c in key if c.isalnum())

            def preprocess_special_fields(d: dict) -> dict:
                d = d.copy()
                # Xử lý trường description nếu là HTML
                desc = d.get('description')
                if isinstance(desc, str) and ('<' in desc and '>' in desc):
                    # Lưu tạm ra file để dùng UnstructuredHTMLLoader
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.html', mode='w', encoding='utf-8') as tmpf:
                        tmpf.write(desc)
                        tmpf_path = tmpf.name
                    try:
                        loader = UnstructuredHTMLLoader(tmpf_path)
                        docs = loader.load()
                        if docs:
                            d['description'] = docs[0].page_content
                    except Exception as e:
                        print(f"Error extracting HTML from description: {str(e)}")
                    finally:
                        os.remove(tmpf_path)
                # Có thể mở rộng cho các trường khác nếu cần
                return d

            def flatten_with_indent(d, level=0):
                lines = []
                indent = '    ' * level  # 4 spaces per level
                for k, v in d.items():
                    key = normalize_key(k)
                    if isinstance(v, dict):
                        lines.append(f"{indent}{key}:")
                        lines.extend(flatten_with_indent(v, level+1))
                    elif isinstance(v, list):
                        for idx, item in enumerate(v):
                            if isinstance(item, dict):
                                lines.append(f"{indent}{key}[{idx}]:")
                                lines.extend(flatten_with_indent(item, level+1))
                            else:
                                lines.append(f"{indent}{key}[{idx}]: {item}")
                    else:
                        lines.append(f"{indent}{key}: {v}")
                return lines

            preprocessed = preprocess_special_fields(record_data)
            content = '\n'.join(flatten_with_indent(preprocessed))
            metadata = {
                "company_id": record_data.get("company_id"),
                "id": record_data.get("id"),
                "record_type": table_name,
            }
            return {
                "content": content,
                "metadata": metadata,
            }
        except Exception as e:
            print(f"Error flattening product JSON: {str(e)}")
            return None



if __name__ == "__main__":
    data = {"event_name": "MODIFY", "record_data": {"images": [{"name": "20250526_LxAf7AAWG5.jpeg", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyNl9MeEFmN0FBV0c1LmpwZWc=", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250526_LxAf7AAWG5.jpeg"}, {"name": "20250528_U33vRWECi5.png", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyOF9VMzN2UldFQ2k1LnBuZw==", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250528_U33vRWECi5.png"}, {"name": "20250528_QF58swceiA.png", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyOF9RRjU4c3djZWlBLnBuZw==", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250528_QF58swceiA.png"}, {"name": "20250528_5gNR7GeFTW.png", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyOF81Z05SN0dlRlRXLnBuZw==", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250528_5gNR7GeFTW.png"}], "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d", "created_at": "2025-05-29T02:18:25.750421+00:00", "description": "<p><strong>Xin l\u01b0u \u00fd: </strong>Ch\u1ea5t li\u1ec7u s\u1ea3n ph\u1ea9m c\u00f3 th\u1ec3 c\u00f3 s\u1ef1 kh\u00e1c bi\u1ec7t, k\u1ec3 c\u1ea3 khi \u0111\u00f3 l\u00e0 c\u00f9ng m\u1ed9t lo\u1ea1i s\u1ea3n ph\u1ea9m</p><p><strong>Ch\u1ea5t li\u1ec7u: </strong>Thun Single k\u1ebft h\u1ee3p c\u00f9ng Thun Da c\u00e1<br />\u00a0\u00a0\u00a0\u00a095% Cotton - 5% Spandex (4 chi\u1ec1u)<br /><strong>\u0110\u1eb7c t\u00ednh: </strong>Tho\u00e1ng kh\u00ed v\u00e0 m\u1ec1m m\u1ea1i. \u0110\u1ed9 co gi\u00e3n t\u1ed1t h\u01a1n Cotton 2 chi\u1ec1u</p><p><strong>Form d\u00e1ng: </strong>R\u1ed9ng (S\u1ea3n ph\u1ea9m n\u00e0y c\u00f3 k\u00edch c\u1ee1 l\u1edbn h\u01a1n b\u00ecnh th\u01b0\u1eddng)</p><p><strong>M\u00e0u s\u1eafc: </strong>\u0110\u01a1n gi\u1ea3n, nh\u00e3 nh\u1eb7n, hi\u1ec7n \u0111\u1ea1i</p><p><strong>\u0110\u1eb7c \u0111i\u1ec3m: </strong>Ho\u1ea1 ti\u1ebft in l\u1ee5a: H\u00ecnh in n\u1ed5i t\u1ea1o c\u1ea3m gi\u00e1c s\u1ed1ng \u0111\u1ed9ng khi nh\u00ecn v\u00e0 ch\u1ea1m. \u0110\u1ed9 b\u1ec1n cao</p><p><strong>H\u01b0\u1edbng d\u1eabn b\u1ea3o qu\u1ea3n:</strong></p><ul><li>Khuy\u1ebfn kh\u00edch gi\u1eb7t tay v\u1edbi n\u01b0\u1edbc gi\u1eb7t chuy\u00ean d\u00f9ng cho b\u00e9\u00a0</li><li>Gi\u1eb7t m\u00e1y v\u1edbi n\u01b0\u1edbc l\u1ea1nh, v\u1eaft \u1edf t\u1ed1c \u0111\u1ed9 th\u1ea5p</li><li>\u00a0Kh\u00f4ng s\u1eed d\u1ee5ng n\u01b0\u1edbc t\u1ea9y / thu\u1ed1c t\u1ea9y</li><li>\u00a0Kh\u00f4ng gi\u1eb7t kh\u00f4</li><li>\u00a0Kh\u00f4ng s\u1eed d\u1ee5ng m\u00e1y s\u1ea5y</li><li>\u00a0L\u1ed9n tr\u00e1i khi gi\u1eb7t v\u00e0 \u1ee7</li><li>Gi\u1eb7t ri\u00eang qu\u1ea7n \u00e1o s\u00e1ng / t\u1ed1i m\u00e0u. C\u00e1c s\u1ea3n ph\u1ea9m \u0111\u1eadm m\u00e0u c\u00f3 kh\u1ea3 n\u0103ng ra m\u00e0u nh\u1ea1t \u1edf nh\u1eefng l\u1ea7n gi\u1eb7t \u0111\u1ea7u ti\u00ean</li></ul>", "sync_record_id": "6b5ca363-434b-4127-8258-3bbf1afc5e51/get_product/product/product_39106909.json", "shortDescription": "<p><strong>Xin l\u01b0u \u00fd: </strong>Ch\u1ea5t li\u1ec7u s\u1ea3n ph\u1ea9m c\u00f3 th\u1ec3 c\u00f3 s\u1ef1 kh\u00e1c bi\u1ec7t, k\u1ec3 c\u1ea3 khi \u0111\u00f3 l\u00e0 c\u00f9ng m\u1ed9t l", "source": {"channel_name": "nhanh", "origin": None, "name": None, "logo": None, "origin_id": None, "id": "6b5ca363-434b-4127-8258-3bbf1afc5e51"}, "variants": [{"optionTitle3": None, "images": [{"name": "20250523_ThL5W2YAHi.jpeg", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyM19UaEw1VzJZQUhpLmpwZWc=", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250523_ThL5W2YAHi.jpeg"}], "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d", "created_at": "2025-05-29T02:18:24.010875+00:00", "unit": None, "updated_at": "2025-05-29T02:18:24.010875+00:00", "inventories": None, "product_id": "02b45b3c-6dc1-4ab2-945c-51de66ba71e3", "name": "\u0110\u1ecf / 18M-2Y-12-14KG", "option3": None, "option1": "\u0110\u1ecf", "id": "841565b2-c275-4286-bea5-351ba411b755", "original_sku": "B-BC-SE08N-03-2DO-18M-2Y", "option2": "18M-2Y-12-14KG", "category": {"parent_category_id": None, "image": None, "has_children": False, "meta_data": None, "name": "Set \u0111\u1ed3 B\u00e9 Trai", "id": "eJxTCk4tUYgpNTA0NIwpNUxNMVZwAvIMUi0VQooSM5UAj50JBA==", "slug": "set-do-be-trai"}, "prices": [{"price_group": {"name": None, "id": "RETAILS"}, "price": "399000"}, {"price_group": {"name": None, "id": "COST"}, "price": "101083"}], "sku": "B-BC-SE08N-03-2DO-18M-2Y", "optionTitle1": "M\u00e0u s\u1eafc", "brand": None, "user": None, "barcode": None, "optionTitle2": "K\u00edch c\u1ee1", "slug": "do-18m-2y-12-14kg-b-bc-se08n-03-2do-18m-2y", "measurements": {"length_value": "0", "height_unit": "cm", "weight_unit": "g", "weight_value": "100", "height_value": "0", "width_unit": "cm", "width_value": "0", "length_unit": "cm"}}, {"optionTitle3": None, "images": [{"name": "20250523_ThL5W2YAHi.jpeg", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyM19UaEw1VzJZQUhpLmpwZWc=", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250523_ThL5W2YAHi.jpeg"}], "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d", "created_at": "2025-05-29T02:18:24.250859+00:00", "unit": None, "updated_at": "2025-05-29T02:18:24.250859+00:00", "inventories": None, "product_id": "02b45b3c-6dc1-4ab2-945c-51de66ba71e3", "name": "\u0110\u1ecf / 2Y-3Y-14-16KG", "option3": None, "option1": "\u0110\u1ecf", "id": "cccc6d10-66a6-4bee-86a8-089b00ec317a", "original_sku": "B-BC-SE08N-03-2DO-2Y-3Y", "option2": "2Y-3Y-14-16KG", "category": {"parent_category_id": None, "image": None, "has_children": False, "meta_data": None, "name": "Set \u0111\u1ed3 B\u00e9 Trai", "id": "eJxTCk4tUYgpNTA0NIwpNUxNMVZwAvIMUi0VQooSM5UAj50JBA==", "slug": "set-do-be-trai"}, "prices": [{"price_group": {"name": None, "id": "RETAILS"}, "price": "399000"}, {"price_group": {"name": None, "id": "COST"}, "price": "101083"}], "sku": "B-BC-SE08N-03-2DO-2Y-3Y", "optionTitle1": "M\u00e0u s\u1eafc", "brand": None, "user": None, "barcode": None, "optionTitle2": "K\u00edch c\u1ee1", "slug": "do-2y-3y-14-16kg-b-bc-se08n-03-2do-2y-3y", "measurements": {"length_value": "0", "height_unit": "cm", "weight_unit": "g", "weight_value": "100", "height_value": "0", "width_unit": "cm", "width_value": "0", "length_unit": "cm"}}, {"optionTitle3": None, "images": [{"name": "20250523_ThL5W2YAHi.jpeg", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyM19UaEw1VzJZQUhpLmpwZWc=", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250523_ThL5W2YAHi.jpeg"}], "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d", "created_at": "2025-05-29T02:18:25.057268+00:00", "unit": None, "updated_at": "2025-05-29T02:18:25.057268+00:00", "inventories": None, "product_id": "02b45b3c-6dc1-4ab2-945c-51de66ba71e3", "name": "\u0110\u1ecf / 3Y-4Y-16-20KG", "option3": None, "option1": "\u0110\u1ecf", "id": "aafdc599-4524-4b95-8c04-09836a882d50", "original_sku": "B-BC-SE08N-03-2DO-3Y-4Y", "option2": "3Y-4Y-16-20KG", "category": {"parent_category_id": None, "image": None, "has_children": False, "meta_data": None, "name": "Set \u0111\u1ed3 B\u00e9 Trai", "id": "eJxTCk4tUYgpNTA0NIwpNUxNMVZwAvIMUi0VQooSM5UAj50JBA==", "slug": "set-do-be-trai"}, "prices": [{"price_group": {"name": None, "id": "RETAILS"}, "price": "399000"}, {"price_group": {"name": None, "id": "COST"}, "price": "101083"}], "sku": "B-BC-SE08N-03-2DO-3Y-4Y", "optionTitle1": "M\u00e0u s\u1eafc", "brand": None, "user": None, "barcode": None, "optionTitle2": "K\u00edch c\u1ee1", "slug": "do-3y-4y-16-20kg-b-bc-se08n-03-2do-3y-4y", "measurements": {"length_value": "0", "height_unit": "cm", "weight_unit": "g", "weight_value": "100", "height_value": "0", "width_unit": "cm", "width_value": "0", "length_unit": "cm"}}, {"optionTitle3": None, "images": [{"name": "20250523_ThL5W2YAHi.jpeg", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyM19UaEw1VzJZQUhpLmpwZWc=", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250523_ThL5W2YAHi.jpeg"}], "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d", "created_at": "2025-05-29T02:18:25.290958+00:00", "unit": None, "updated_at": "2025-05-29T02:18:25.290958+00:00", "inventories": None, "product_id": "02b45b3c-6dc1-4ab2-945c-51de66ba71e3", "name": "\u0110\u1ecf / 5Y-6Y-20-25KG", "option3": None, "option1": "\u0110\u1ecf", "id": "2e1e96e8-7a0f-4bac-a07a-67b9aa07d7e1", "original_sku": "B-BC-SE08N-03-2DO-5Y-6Y", "option2": "5Y-6Y-20-25KG", "category": {"parent_category_id": None, "image": None, "has_children": False, "meta_data": None, "name": "Set \u0111\u1ed3 B\u00e9 Trai", "id": "eJxTCk4tUYgpNTA0NIwpNUxNMVZwAvIMUi0VQooSM5UAj50JBA==", "slug": "set-do-be-trai"}, "prices": [{"price_group": {"name": None, "id": "RETAILS"}, "price": "399000"}, {"price_group": {"name": None, "id": "COST"}, "price": "101083"}], "sku": "B-BC-SE08N-03-2DO-5Y-6Y", "optionTitle1": "M\u00e0u s\u1eafc", "brand": None, "user": None, "barcode": None, "optionTitle2": "K\u00edch c\u1ee1", "slug": "do-5y-6y-20-25kg-b-bc-se08n-03-2do-5y-6y", "measurements": {"length_value": "0", "height_unit": "cm", "weight_unit": "g", "weight_value": "100", "height_value": "0", "width_unit": "cm", "width_value": "0", "length_unit": "cm"}}, {"optionTitle3": None, "images": [{"name": "20250523_ThL5W2YAHi.jpeg", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyM19UaEw1VzJZQUhpLmpwZWc=", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250523_ThL5W2YAHi.jpeg"}], "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d", "created_at": "2025-05-29T02:18:25.494559+00:00", "unit": None, "updated_at": "2025-05-29T02:18:25.494559+00:00", "inventories": None, "product_id": "02b45b3c-6dc1-4ab2-945c-51de66ba71e3", "name": "\u0110\u1ecf / 7Y-8Y-25-31KG", "option3": None, "option1": "\u0110\u1ecf", "id": "80e383a6-dab9-48e3-919a-3da2cd9ecd4d", "original_sku": "B-BC-SE08N-03-2DO-7Y-8Y", "option2": "7Y-8Y-25-31KG", "category": {"parent_category_id": None, "image": None, "has_children": False, "meta_data": None, "name": "Set \u0111\u1ed3 B\u00e9 Trai", "id": "eJxTCk4tUYgpNTA0NIwpNUxNMVZwAvIMUi0VQooSM5UAj50JBA==", "slug": "set-do-be-trai"}, "prices": [{"price_group": {"name": None, "id": "RETAILS"}, "price": "399000"}, {"price_group": {"name": None, "id": "COST"}, "price": "101083"}], "sku": "B-BC-SE08N-03-2DO-7Y-8Y", "optionTitle1": "M\u00e0u s\u1eafc", "brand": None, "user": None, "barcode": None, "optionTitle2": "K\u00edch c\u1ee1", "slug": "do-7y-8y-25-31kg-b-bc-se08n-03-2do-7y-8y", "measurements": {"length_value": "0", "height_unit": "cm", "weight_unit": "g", "weight_value": "100", "height_value": "0", "width_unit": "cm", "width_value": "0", "length_unit": "cm"}}, {"optionTitle3": None, "images": [{"name": "20250523_ThL5W2YAHi.jpeg", "id": "OWU2MWQxODctNDI2YS00NWVjLTkxNGQtN2FlYThjYTdkNDJkL2ltYWdlcy9wcm9kdWN0cy8yMDI1MDUyM19UaEw1VzJZQUhpLmpwZWc=", "url": "https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/products/20250523_ThL5W2YAHi.jpeg"}], "company_id": "9e61d187-426a-45ec-914d-7aea8ca7d42d", "created_at": "2025-05-29T02:18:25.690913+00:00", "unit": None, "updated_at": "2025-05-29T02:18:25.690913+00:00", "inventories": None, "product_id": "02b45b3c-6dc1-4ab2-945c-51de66ba71e3", "name": "\u0110\u1ecf / 9Y-10Y-31-38KG", "option3": None, "option1": "\u0110\u1ecf", "id": "b1fb23dc-2081-4829-8521-621490da491d", "original_sku": "B-BC-SE08N-03-2DO-9Y-10Y", "option2": "9Y-10Y-31-38KG", "category": {"parent_category_id": None, "image": None, "has_children": False, "meta_data": None, "name": "Set \u0111\u1ed3 B\u00e9 Trai", "id": "eJxTCk4tUYgpNTA0NIwpNUxNMVZwAvIMUi0VQooSM5UAj50JBA==", "slug": "set-do-be-trai"}, "prices": [{"price_group": {"name": None, "id": "RETAILS"}, "price": "399000"}, {"price_group": {"name": None, "id": "COST"}, "price": "101083"}], "sku": "B-BC-SE08N-03-2DO-9Y-10Y", "optionTitle1": "M\u00e0u s\u1eafc", "brand": None, "user": None, "barcode": None, "optionTitle2": "K\u00edch c\u1ee1", "slug": "do-9y-10y-31-38kg-b-bc-se08n-03-2do-9y-10y", "measurements": {"length_value": "0", "height_unit": "cm", "weight_unit": "g", "weight_value": "100", "height_value": "0", "width_unit": "cm", "width_value": "0", "length_unit": "cm"}}], "tags": None, "updated_at": "2025-06-10T10:29:13.376649+00:00", "inventories": None, "publish": False, "meta_data": None, "name": "Set Thun Form R\u1ed9ng B\u00e9 Trai Kem Ph\u1ed1i \u0110\u1ecf Saigon 03", "options": [{"values": ["\u0110\u1ecf"], "name": "M\u00e0u s\u1eafc"}, {"values": ["3Y-4Y-16-20KG", "5Y-6Y-20-25KG", "9Y-10Y-31-38KG", "7Y-8Y-25-31KG", "2Y-3Y-14-16KG", "18M-2Y-12-14KG"], "name": "K\u00edch c\u1ee1"}], "id": "02b45b3c-6dc1-4ab2-945c-51de66ba71e3", "category": {"parent_category_id": None, "image": None, "has_children": False, "meta_data": None, "name": "Set \u0111\u1ed3 B\u00e9 Trai", "id": "eJxTCk4tUYgpNTA0NIwpNUxNMVZwAvIMUi0VQooSM5UAj50JBA==", "slug": "set-do-be-trai"}, "prices": None, "sku": "B-BC-SE08N-03-2DO", "barcode": None, "brand": None, "user": None, "slug": "set-thun-form-rong-be-trai-kem-phoi-do-saigon-03-b-bc-se08n-03-2do", "measurements": {"length_value": "0", "height_unit": "cm", "weight_unit": "g", "weight_value": "0", "height_value": "0", "width_unit": "cm", "width_value": "0", "length_unit": "cm"}}, "table_name": "product"}
    json_processor = JsonProcessor()
    result = json_processor.process_json(data['record_data'], data['table_name'])
    if result:
        print("Content:\n", result["content"])
        print("Metadata:\n", result["metadata"])
    else:
        print("Failed to process product JSON.")

