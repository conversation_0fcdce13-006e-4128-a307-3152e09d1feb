from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_postgres import PGVector
from langchain_core.documents import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from onexbots.shared.config import SharedSettings
import logging
import psycopg
import asyncio
import asyncpg
from sqlalchemy import create_engine
from datetime import datetime
import uuid
from sqlalchemy.ext.asyncio import create_async_engine
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
import re
from typing import Tuple

logger = logging.getLogger(__name__)


class KnowledgeDocument(BaseModel):
    knowledge_id: str
    content: str
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    source: str
    relevance_score: Optional[float] = None
    summary: Optional[str] = None


class Embedding(BaseModel):
    vector: List[float]
    metadata: dict


class EmbeddingService:
    def __init__(self, settings: SharedSettings):
        self.settings = settings
        self.embeddings = OpenAIEmbeddings(
            model=settings.EMBEDDING_MODEL, openai_api_key=settings.OPENAI_API_KEY
        )
        # For PostgreSQL connection string
        self.connection_string = f"postgresql+psycopg://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
        # For asyncpg operations
        self.async_connection_string = f"postgresql+asyncpg://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
        self._vector_store = None
        self._async_vector_store = None
        self._async_engine = None
        self._sync_engine = None
        self._ensure_vector_extension()

    def _ensure_vector_extension(self):
        """Ensure vector extension exists using synchronous connection."""
        try:
            # Create synchronous connection
            conn = psycopg.connect(
                dbname=self.settings.POSTGRES_DB,
                user=self.settings.POSTGRES_USER,
                password=self.settings.POSTGRES_PASSWORD,
                host=self.settings.POSTGRES_HOST,
                port=self.settings.POSTGRES_PORT,
            )
            conn.autocommit = True
            cursor = conn.cursor()
            cursor.execute("CREATE EXTENSION IF NOT EXISTS vector")
            cursor.close()
            conn.close()
            logger.info("Vector extension created successfully")
        except Exception as e:
            logger.error(f"Failed to create vector extension: {str(e)}")
            raise

    async def initialize_table(self):
        """Initialize the database table structure if it doesn't exist yet."""
        try:
            # Connect directly with asyncpg

            conn = await asyncpg.connect(
                user=self.settings.POSTGRES_USER,
                password=self.settings.POSTGRES_PASSWORD,
                host=self.settings.POSTGRES_HOST,
                port=self.settings.POSTGRES_PORT,
                database=self.settings.POSTGRES_DB,
            )

            # Close connection
            await conn.close()

        except Exception as e:
            logger.error(f"Failed to initialize table: {str(e)}")
            raise

    async def initialize(self, async_mode: bool = False):
        """Initialize the vector store asynchronously."""
        if self._vector_store is None:
            try:
                # Initialize database table structure first
                await self.initialize_table()

                if async_mode:
                    self._async_engine = create_async_engine(
                        self.connection_string,
                        pool_size=5,
                        max_overflow=10,
                        pool_timeout=30,
                        pool_recycle=1800,
                    )
                    self._vector_store = PGVector(
                        collection_name="embeddings",
                        connection=self._async_engine,
                        embeddings=self.embeddings,
                        use_jsonb=True,
                    )
                else:
                    # Create sync engine for vector store operations
                    self._sync_engine = create_engine(
                        self.connection_string,
                        pool_size=5,
                        max_overflow=10,
                        pool_timeout=30,
                        pool_recycle=1800,
                    )

                    # Create async engine for other operations

                    # Create the vector store with sync engine
                    self._vector_store = PGVector(
                        collection_name="embeddings",
                        connection=self._sync_engine,
                        embeddings=self.embeddings,
                        use_jsonb=True,
                    )

                logger.info("Vector store initialized successfully")
            except Exception as e:
                logger.error(f"Failed to create vector store: {str(e)}")
                if self._async_engine:
                    await self._async_engine.dispose()
                    self._async_engine = None
                if self._sync_engine:
                    self._sync_engine.dispose()
                    self._sync_engine = None
                raise

    async def cleanup(self):
        """Cleanup resources and close connections."""
        try:
            if self._async_engine:
                await self._async_engine.dispose()
                self._async_engine = None
            self._vector_store = None
        except Exception as e:
            logger.error(f"Error during EmbeddingService cleanup: {str(e)}")

    async def get_vector_store(self, async_mode: bool = False):
        """Get or create vector store instance."""
        if self._vector_store is None:
            await self.initialize(async_mode)
        return self._vector_store

    def extract_and_remove_bot_direction(self, content: str):
        """Extracts [bot_direction]:[...] from content and returns (direction or None, cleaned_content)."""
        match = re.search(
            r"^[ \t]*\[bot_direction\]:\[(.+?)\]", content, re.MULTILINE | re.DOTALL
        )
        if match:
            direction = " ".join(match.group(1).split())
            cleaned_content = re.sub(
                r"^[ \t]*\[bot_direction\]:\[(.+?)\]\s*\n?",
                "",
                content,
                flags=re.MULTILINE | re.DOTALL,
            )
            return direction, cleaned_content.strip()
        return None, content

    async def _generate_direction(self, content: str) -> Tuple[str, str]:
        """Generate a direction for AI: when to use this tool and what it is for. If [bot_direction]:[direction...] is present in content, use and remove it."""
        try:
            # Use helper to extract and remove bot_direction
            chat = ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0,
                openai_api_key=self.settings.OPENAI_API_KEY,
            )
            prompt = ChatPromptTemplate.from_messages(
                [
                    (
                        "system",
                        "You are a helpful assistant that writes tool usage directions for AI. Given the following content, explain in 2-3 sentences when the AI should use this tool and what the tool is for. Be clear and concise. The content is a chunk of a knowledge document.",
                    ),
                    ("user", "{content}"),
                ]
            )
            chain = prompt | chat | StrOutputParser()
            direction = await chain.ainvoke({"content": content})
            return direction.strip()
        except Exception as e:
            logger.error(f"Error generating direction: {str(e)}")
            return content[:200] + "..." if len(content) > 200 else content

    async def generate_embeddings(
        self, input_text: str, metadata: dict, s3_key: Optional[str] = None
    ) -> Optional[KnowledgeDocument]:
        """Generate embeddings for input text and store in vector store.

        Args:
            input_text: The text content to generate embeddings for
            metadata: Additional metadata for the document
            s3_key: Optional S3 key to extract knowledge_id from
        """
        try:
            # Extract knowledge_id from S3 key if provided
            knowledge_id = None
            if s3_key:
                # Assuming S3 key format: knowledge/{knowledge_id}/{filename}
                parts = s3_key.split("/")
                if len(parts) >= 2:
                    knowledge_id = parts[1]  # Get the knowledge_id from the path
                    metadata["knowledge_id"] = knowledge_id
                else:
                    logger.warning(
                        f"Invalid S3 key format for knowledge_id extraction: {s3_key}"
                    )

            # If no knowledge_id in metadata, generate a new one
            if not knowledge_id and "knowledge_id" not in metadata:
                knowledge_id = str(uuid.uuid4())
                metadata["knowledge_id"] = knowledge_id

            # Get vector store
            vector_store = await self.get_vector_store()

            # Generate AI direction and possibly cleaned content
            direction = await self._generate_direction(input_text)
            metadata["direction"] = direction
            special_instruction, cleaned_input_text = (
                self.extract_and_remove_bot_direction(input_text)
            )
            metadata["special_instruction"] = special_instruction
            # Create document with metadata
            doc = Document(
                page_content=cleaned_input_text,
                metadata={
                    **metadata,
                    "source": metadata.get("source", "s3"),
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                },
            )

            # Use synchronous add_documents instead of async version
            def add_documents():
                vector_store.add_documents([doc])

            # Run synchronous method in a thread
            await asyncio.get_event_loop().run_in_executor(None, add_documents)

            return KnowledgeDocument(
                knowledge_id=knowledge_id,
                content=cleaned_input_text,
                metadata=doc.metadata,
                created_at=datetime.fromisoformat(doc.metadata["created_at"]),
                updated_at=datetime.fromisoformat(doc.metadata["updated_at"]),
                source=doc.metadata["source"],
            )

        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            return None

    async def _get_document_by_knowledge_id(
        self, knowledge_id: str
    ) -> Optional[List[Document]]:
        """Get all document chunks by knowledge_id using metadata filter."""
        try:
            vector_store = await self.get_vector_store()

            def search_document():
                # Search for all documents with matching knowledge_id
                return vector_store.similarity_search_with_score(
                    "",  # Empty query to get all documents
                    k=100,  # Increased to get all chunks
                    filter={"knowledge_id": knowledge_id},
                )

            results = await asyncio.get_event_loop().run_in_executor(
                None, search_document
            )

            # Extract documents from results
            return [doc for doc, _ in results] if results else None

        except Exception as e:
            logger.error(f"Error getting document by knowledge_id: {str(e)}")
            return None

    async def add_or_update_knowledge_document(
        self, knowledge_id: str, content: str, metadata: Dict[str, Any], source: str
    ) -> KnowledgeDocument:
        """Add a new knowledge document or update existing one based on knowledge_id."""
        try:
            vector_store = await self.get_vector_store()

            # Check if document exists
            existing_docs = await self._get_document_by_knowledge_id(knowledge_id)

            # Initialize text splitter
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                length_function=len,
                separators=["\n\n", "\n", " ", ""],
            )

            # Split content into chunks
            # Generate direction and possibly cleaned content
            direction = await self._generate_direction(content)
            metadata["direction"] = direction
            special_instruction, cleaned_content = (
                self.extract_and_remove_bot_direction(content)
            )
            metadata["special_instruction"] = special_instruction
            chunks = text_splitter.split_text(cleaned_content)

            # Create documents for each chunk
            docs = []
            for i, chunk in enumerate(chunks):
                chunk_id = f"{knowledge_id}_chunk_{i}"
                doc = Document(
                    page_content=chunk,
                    metadata={
                        **metadata,
                        "knowledge_id": knowledge_id,
                        "chunk_id": chunk_id,
                        "source": source,
                        "chunk_index": i,
                        "total_chunks": len(chunks),
                        "created_at": (
                            existing_docs[0].metadata.get(
                                "created_at", datetime.utcnow().isoformat()
                            )
                            if existing_docs
                            else datetime.utcnow().isoformat()
                        ),
                        "updated_at": datetime.utcnow().isoformat(),
                    },
                )
                docs.append(doc)

            if existing_docs:
                # Update existing document
                def update_document():
                    # Delete old documents
                    vector_store.delete([doc.id for doc in existing_docs])
                    # Add updated documents
                    return vector_store.add_documents(docs)

            else:
                # Add new documents
                def update_document():
                    return vector_store.add_documents(docs)

            doc_ids = await asyncio.get_event_loop().run_in_executor(
                None, update_document
            )

            return KnowledgeDocument(
                knowledge_id=knowledge_id,
                content=cleaned_content,
                metadata={
                    **metadata,
                    "source": source,
                    "created_at": datetime.fromisoformat(
                        docs[0].metadata["created_at"]
                    ),
                    "updated_at": datetime.fromisoformat(
                        docs[0].metadata["updated_at"]
                    ),
                    "total_chunks": len(chunks),
                },
                created_at=datetime.fromisoformat(docs[0].metadata["created_at"]),
                updated_at=datetime.fromisoformat(docs[0].metadata["updated_at"]),
                source=source,
            )

        except Exception as e:
            logger.error(f"Error adding/updating knowledge document: {str(e)}")
            raise

    async def _get_record_documents(
        self, filters: dict = None
    ) -> Optional[List[Document]]:
        """Get all document chunks by id using metadata filter."""
        try:
            vector_store = await self.get_vector_store()

            def search_document():
                # Search for all documents with matching knowledge_id
                return vector_store.similarity_search_with_score(
                    "",  # Empty query to get all documents
                    k=100,  # Increased to get all chunks
                    filter=filters or {},
                )

            results = await asyncio.get_event_loop().run_in_executor(
                None, search_document
            )

            # Extract documents from results
            return [doc for doc, _ in results] if results else None

        except Exception as e:
            logger.error(f"Error getting document by knowledge_id: {str(e)}")
            return None

    async def add_or_update_record_document(self, processed_record: dict) -> bool:
        """Add or update a record document by record_id."""
        try:
            vector_store = await self.get_vector_store()
            existing_docs = await self._get_record_documents(
                filters={"id": processed_record["metadata"]["id"]}
            )
            doc = Document(
                page_content=processed_record["content"],
                metadata={
                    **processed_record["metadata"],
                    "source": "sqs",
                    "created_at": (
                        existing_docs[0].metadata.get(
                            "created_at", datetime.utcnow().isoformat()
                        )
                        if existing_docs
                        else datetime.utcnow().isoformat()
                    ),
                    "updated_at": datetime.utcnow().isoformat(),
                },
            )
            if existing_docs:
                # Update existing document
                def update_document():
                    # Delete old documents
                    vector_store.delete([doc.id for doc in existing_docs])
                    # Add updated documents
                    return vector_store.add_documents([doc])

            else:
                # Add new documents
                def update_document():
                    return vector_store.add_documents([doc])

            await asyncio.get_event_loop().run_in_executor(None, update_document)
            return True
        except Exception as e:
            logger.error(f"Error deleting knowledge document: {str(e)}")
            return False

    async def delete_record_document(self, record_id: str) -> bool:
        """Delete a knowledge document by knowledge_id."""
        try:
            vector_store = await self.get_vector_store()
            existing_docs = await self._get_document_by_knowledge_id(record_id)
            if not existing_docs:
                logger.warning(f"No document found with knowledge_id: {record_id}")
                return False

            def delete_documents():
                vector_store.delete([doc.metadata.get("id") for doc in existing_docs])

            await asyncio.get_event_loop().run_in_executor(None, delete_documents)
            return True
        except Exception as e:
            logger.error(f"Error deleting knowledge document: {str(e)}")
            return False

    async def search_similar(
        self,
        query: str,
        limit: int = 5,
        min_similarity: float = 0.7,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[KnowledgeDocument]:
        """Search for similar documents with enhanced filtering and scoring."""
        try:
            vector_store = await self.get_vector_store()

            def similarity_search():
                return vector_store.similarity_search_with_score(
                    query,
                    k=limit * 10,  # Increased to get more chunks for grouping
                    filter=filters,
                )

            docs_with_scores = await asyncio.get_event_loop().run_in_executor(
                None, similarity_search
            )

            # Group chunks by knowledge_id
            grouped_docs = {}
            for doc, score in docs_with_scores:
                knowledge_id = doc.metadata.get("knowledge_id")
                if knowledge_id not in grouped_docs:
                    grouped_docs[knowledge_id] = {
                        "chunks": [],
                        "scores": [],
                        "metadata": doc.metadata,
                    }
                grouped_docs[knowledge_id]["chunks"].append(doc)
                grouped_docs[knowledge_id]["scores"].append(score)

            # Sort chunks within each group and combine content
            results = []
            for knowledge_id, group in grouped_docs.items():
                # Sort chunks by index
                sorted_chunks = sorted(
                    group["chunks"], key=lambda x: x.metadata.get("chunk_index", 0)
                )
                # Calculate average score
                avg_score = sum(group["scores"]) / len(group["scores"])

                if avg_score >= min_similarity:
                    # Combine content from all chunks
                    combined_content = "\n".join(
                        chunk.page_content for chunk in sorted_chunks
                    )

                    results.append(
                        KnowledgeDocument(
                            knowledge_id=knowledge_id,
                            content=combined_content,
                            metadata=group["metadata"],
                            created_at=datetime.fromisoformat(
                                group["metadata"].get(
                                    "created_at", datetime.utcnow().isoformat()
                                )
                            ),
                            updated_at=datetime.fromisoformat(
                                group["metadata"].get(
                                    "updated_at", datetime.utcnow().isoformat()
                                )
                            ),
                            source=group["metadata"].get("source", ""),
                            relevance_score=avg_score,
                        )
                    )
                else:
                    logger.info(
                        f"Skipping document with low similarity score: {avg_score}"
                    )

            # Sort results by average score and limit
            results.sort(key=lambda x: x.relevance_score or 0, reverse=True)
            results = results[:limit]

            if not results:
                logger.warning(
                    f"No documents found with similarity score above {min_similarity}"
                )

            return results

        except Exception as e:
            logger.error(f"Error searching similar embeddings: {str(e)}")
            return []

    async def delete_knowledge_document(self, knowledge_id: str) -> bool:
        """Delete a knowledge document by knowledge_id."""
        try:
            vector_store = await self.get_vector_store()
            doc = await self._get_document_by_knowledge_id(knowledge_id)

            if not doc:
                logger.warning(f"No document found with knowledge_id: {knowledge_id}")
                return False

            def delete_document():
                vector_store.delete([doc.metadata.get("id") for doc in doc])

            await asyncio.get_event_loop().run_in_executor(None, delete_document)
            return True

        except Exception as e:
            logger.error(f"Error deleting knowledge document: {str(e)}")
            return False

    async def get_relevant_context(self, query: str, max_tokens: int = 2000) -> str:
        """Get relevant context for a query, optimized for AI chat responses."""
        try:
            # Get similar documents
            docs = await self.search_similar(query, limit=5, min_similarity=0.7)

            # Sort by relevance score
            docs.sort(key=lambda x: x.relevance_score or 0, reverse=True)

            # Build context string
            context = ""
            current_tokens = 0

            for doc in docs:
                # Add document content if it fits within token limit
                doc_tokens = len(doc.content.split())
                if current_tokens + doc_tokens <= max_tokens:
                    context += f"\n\nSource: {doc.source}\nKnowledge ID: {doc.knowledge_id}\nContent: {doc.content}"
                    current_tokens += doc_tokens
                else:
                    break

            return context.strip()

        except Exception as e:
            logger.error(f"Error getting relevant context: {str(e)}")
            return ""

    async def retriever(self, knowledge_id: str):
        vector_store = await self.get_vector_store(async_mode=True)
        return vector_store.as_retriever(
            search_kwargs={
                "filter": {"knowledge_id": knowledge_id},
            }
        )

    async def product_retriever(
        self,
        company_id: str,
        record_type: str,
        filters: Optional[Dict[str, Any]] = None,
    ):
        """
        Return a retriever for products filtered by company_id, record_type, and any additional filters.
        """
        vector_store = await self.get_vector_store(async_mode=True)
        base_filter = {"company_id": company_id, "record_type": record_type}
        if filters:
            base_filter.update(filters)
        return vector_store.as_retriever(
            search_kwargs={
                "filter": base_filter,
            }
        )
