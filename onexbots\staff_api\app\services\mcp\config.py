from typing import Dict, Any, List, Optional
from pydantic import BaseModel, validator

class MCPConfig(BaseModel):
    """Configuration for Model Context Protocol."""
    enabled: bool = True
    server_url: Optional[str] = None
    enabled_adapters: Optional[List[str]] = None
    adapters_config: Optional[Dict[str, Dict[str, Any]]] = None

    @validator('enabled', pre=True, always=True)
    def validate_enabled(cls, v, values):
        """Automatically disable MCP if required fields are missing."""
        if values.get('server_url') is None or values.get('enabled_adapters') is None:
            return False
        return v

    @validator('server_url', pre=True, always=True)
    def validate_server_url(cls, v):
        """Set default server URL if None."""
        return v or "http://localhost:8000"

    @validator('enabled_adapters', pre=True, always=True)
    def validate_enabled_adapters(cls, v):
        """Set default enabled adapters if None."""
        return v or ["content_filter", "rate_limiter"]

    @validator('adapters_config', pre=True, always=True)
    def validate_adapters_config(cls, v):
        """Set default adapters configuration if None."""
        if v is None:
            return {
                "content_filter": {
                    "enabled": True,
                    "action": "block",
                    "patterns": ["inappropriate_word_1", "inappropriate_word_2"]
                },
                "rate_limiter": {
                    "enabled": True,
                    "action": "delay",
                    "max_messages": 10,
                    "time_window": 60
                }
            }
        return v 