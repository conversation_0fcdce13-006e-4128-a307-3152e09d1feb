from .vi_prompt import VietnameseSystemPrompt
from .en_prompt import EnglishSystemPrompt
from .ja_prompt import JapaneseSystemPrompt
from .zh_prompt import ChineseSystemPrompt

PROMPT_REGISTRY = {
    "vi": VietnameseSystemPrompt,
    "en": EnglishSystemPrompt,
    "ja": JapaneseSystemPrompt,
    "zh": ChineseSystemPrompt,
}


def get_system_prompt(staff , metadata):
    language_code = (
        staff.get("configuration", {}).get("personality", {}).get("language", "en")
    )
    prompt_class = PROMPT_REGISTRY.get(
        language_code, EnglishSystemPrompt
    )  # Default to English
    return prompt_class(staff ,metadata).build()
