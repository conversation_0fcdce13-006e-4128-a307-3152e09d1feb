import re
import logging
from typing import Dict, Any, List, Union, Optional
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from langchain_core.messages import BaseMessage
from langchain_core.runnables import RunnableConfig

from .base import BaseMCPAdapter, MCPAction
from .registry import MCPRegistry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get registry instance
mcp_registry = MCPRegistry()

class ContentFilterAdapter(BaseMCPAdapter):
    """Content filter adapter for Model Context Protocol."""
    
    def __init__(self, config: Dict[str, Any]):
        self._is_content_filter = True  # Mark as content filter for initialization
        super().__init__(config)
        logger.debug(f"Initialized ContentFilterAdapter with {len(self.patterns)} patterns")
    
    def _validate_config(self) -> None:
        """Validate adapter configuration."""
        # Ensure required fields exist with defaults
        if "patterns" not in self.config:
            self.config["patterns"] = []
        if "action" not in self.config:
            self.config["action"] = MCPAction.BLOCK
        if "replacement" not in self.config:
            self.config["replacement"] = "***"
        
        # Validate fields
        if not self.config["patterns"]:
            logger.warning("Content filter has no patterns configured")
        if self.config["action"] not in [MCPAction.BLOCK, MCPAction.MODIFY]:
            raise ValueError("Content filter only supports BLOCK and MODIFY actions")
        if self.config["action"] == MCPAction.MODIFY and not self.config["replacement"]:
            raise ValueError("MODIFY action requires replacement text")
    
    async def process_messages(
        self,
        messages: List[Union[HumanMessage, AIMessage, SystemMessage]],
        user_id: str
    ) -> List[Union[HumanMessage, AIMessage, SystemMessage]]:
        """Process messages through content filter."""
        try:
            processed_messages = []
            for message in messages:
                if not isinstance(message, HumanMessage):
                    processed_messages.append(message)
                    continue
                
                content = message.content
                if not content:
                    processed_messages.append(message)
                    continue
                
                # Check for patterns
                for pattern in self.patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        if self.action == MCPAction.BLOCK:
                            logger.info(f"Blocked message containing pattern: {pattern}")
                            return []  # Return empty list to block message
                        elif self.action == MCPAction.MODIFY:
                            content = re.sub(pattern, self.replacement, content, flags=re.IGNORECASE)
                            logger.info(f"Modified message containing pattern: {pattern}")
                
                # Create new message with processed content
                processed_message = HumanMessage(content=content)
                processed_messages.append(processed_message)
            
            return processed_messages
            
        except Exception as e:
            logger.error(f"Error processing messages: {str(e)}")
            return messages
    
    async def _process_message(self, message: str, user_id: str) -> str:
        """Process a single message."""
        try:
            # Check for patterns
            for pattern in self.patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    if self.action == MCPAction.BLOCK:
                        logger.info(f"Blocked message containing pattern: {pattern}")
                        return ""  # Return empty string to block message
                    elif self.action == MCPAction.MODIFY:
                        message = re.sub(pattern, self.replacement, message, flags=re.IGNORECASE)
                        logger.info(f"Modified message containing pattern: {pattern}")
            
            return message
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return message

# Register the adapter
mcp_registry.register_rule("content_filter", ContentFilterAdapter) 