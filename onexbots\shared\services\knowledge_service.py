import logging
from typing import Optional, Dict, Any, List
from ..config import settings
from .base_api_service import BaseAPIService
from .embedding_service import EmbeddingService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeService(BaseAPIService[Dict[str, Any]]):
    """Service for interacting with the Knowledge API."""

    def __init__(self):
        super().__init__()
        self.embedding_service = EmbeddingService(settings)

    def create(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new knowledge item."""
        return self._make_request("POST", "/knowledge/text", json=data)

    def get(self, id: str) -> Optional[Dict[str, Any]]:
        """Get a knowledge item by ID."""
        return self._make_request("GET", f"/knowledge/{id}")

    def list(self) -> Optional[List[Dict[str, Any]]]:
        """List all knowledge items."""
        return self._make_request("GET", "/knowledge")

    def delete(self, id: str) -> bool:
        """Delete a knowledge item by ID."""
        result = self._make_request("DELETE", f"/knowledge/{id}")
        return result is not None

    def create_from_text(
        self, text: str, metadata: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Create knowledge from text."""
        return self._make_request(
            "POST", "/knowledge/text", json={"text": text, "metadata": metadata}
        )

    def create_from_url(
        self, url: str, metadata: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Create knowledge from URL."""
        return self._make_request(
            "POST", "/knowledge/url", json={"url": url, "metadata": metadata}
        )

    def check_status(self, id: str) -> Optional[Dict[str, Any]]:
        """Check knowledge processing status."""
        return self._make_request("GET", f"/knowledge/{id}/status")

    def update_status(
        self, id: str, status: str, company_id: str
    ) -> Optional[Dict[str, Any]]:
        """Update knowledge status."""
        return self._make_request(
            "PUT",
            f"/knowledge/{id}/status?company_id={company_id}",
            json={"status": status},
        )

    async def get_relevant_context(
        self,
        query: str,
        max_tokens: int = 2000,
        min_similarity: float = 0.7,
        knowledge_ids: Optional[List[str]] = None,
    ) -> str:
        """Get relevant context for a query with optional knowledge ID filtering.

        Args:
            query: The query to find relevant context for
            max_tokens: Maximum number of tokens in the context
            min_similarity: Minimum similarity score for documents
            knowledge_ids: Optional list of knowledge IDs to filter by

        Returns:
            str: Formatted context string or empty string if no relevant context found
        """
        try:
            # Create filters if knowledge_ids provided
            filters = None
            if knowledge_ids:
                filters = {"knowledge_id": {"$in": knowledge_ids}}

            # Get similar documents with filters
            docs = await self.embedding_service.search_similar(
                query=query, limit=5, min_similarity=min_similarity, filters=filters
            )

            if not docs:
                return ""

            # Sort by relevance score
            docs.sort(key=lambda x: x.relevance_score or 0, reverse=True)

            # Build context string
            context = ""
            current_tokens = 0

            for doc in docs:
                # Add document content if it fits within token limit
                doc_tokens = len(doc.content.split())
                if current_tokens + doc_tokens <= max_tokens:
                    context += f"\n\nSource: {doc.source}\nKnowledge ID: {doc.knowledge_id}\nContent: {doc.content}"
                    current_tokens += doc_tokens
                else:
                    break

            return context.strip()

        except Exception as e:
            logger.error(f"Error getting relevant context: {str(e)}")
            return ""

    async def retriever(self, knowledge_id: str):
        return await self.embedding_service.retriever(knowledge_id)
