from typing import Dict, Any, List, Optional, Union
from collections import defaultdict
import time
import logging
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from langchain_core.messages import BaseMessage
from langchain_core.runnables import RunnableConfig
import asyncio

from .base import BaseMCPAdapter, MCPAction
from .registry import MCPRegistry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get registry instance
mcp_registry = MCPRegistry()

class RateLimiterAdapter(BaseMCPAdapter):
    """Rate limiter adapter for Model Context Protocol."""
    
    def __init__(self, config: Dict[str, Any]):
        self._is_rate_limiter = True  # Mark as rate limiter for initialization
        super().__init__(config)
        logger.debug(f"Initialized RateLimiterAdapter with max_messages={self.max_messages}, time_window={self.time_window}")
    
    def _validate_config(self) -> None:
        """Validate adapter configuration."""
        # Ensure required fields exist with defaults
        if "max_messages" not in self.config:
            self.config["max_messages"] = 10
        if "time_window" not in self.config:
            self.config["time_window"] = 60
        if "action" not in self.config:
            self.config["action"] = MCPAction.DELAY
        
        # Validate fields
        if not isinstance(self.config["max_messages"], int) or self.config["max_messages"] <= 0:
            raise ValueError("max_messages must be a positive integer")
        if not isinstance(self.config["time_window"], (int, float)) or self.config["time_window"] <= 0:
            raise ValueError("time_window must be a positive number")
        if self.config["action"] not in [MCPAction.BLOCK, MCPAction.DELAY]:
            raise ValueError("Rate limiter only supports BLOCK and DELAY actions")
    
    async def process_messages(
        self,
        messages: List[Union[HumanMessage, AIMessage, SystemMessage]],
        user_id: str
    ) -> List[Union[HumanMessage, AIMessage, SystemMessage]]:
        """Process messages through rate limiter."""
        try:
            processed_messages = []
            current_time = time.time()
            
            # Clean up old messages
            self.user_history[user_id] = [
                timestamp for timestamp in self.user_history.get(user_id, [])
                if current_time - timestamp < self.time_window
            ]
            
            # Check rate limit
            if len(self.user_history[user_id]) >= self.max_messages:
                if self.action == MCPAction.BLOCK:
                    logger.info(f"Blocked message from user {user_id} due to rate limit")
                    return []  # Return empty list to block message
                elif self.action == MCPAction.DELAY:
                    # Calculate delay needed
                    oldest_message = min(self.user_history[user_id])
                    delay_needed = self.time_window - (current_time - oldest_message)
                    if delay_needed > 0:
                        logger.info(f"Delaying message from user {user_id} by {delay_needed:.2f} seconds")
                        await asyncio.sleep(delay_needed)
            
            # Process messages
            for message in messages:
                if isinstance(message, HumanMessage):
                    self.user_history[user_id].append(current_time)
                processed_messages.append(message)
            
            return processed_messages
            
        except Exception as e:
            logger.error(f"Error processing messages: {str(e)}")
            return messages
    
    async def _process_message(self, message: str, user_id: str) -> str:
        """Process a single message."""
        try:
            current_time = time.time()
            
            # Clean up old messages
            self.user_history[user_id] = [
                timestamp for timestamp in self.user_history[user_id]
                if current_time - timestamp < self.time_window
            ]
            
            # Check rate limit
            if len(self.user_history[user_id]) >= self.max_messages:
                if self.action == MCPAction.BLOCK:
                    logger.info(f"Blocked message from user {user_id} due to rate limit")
                    return ""  # Return empty string to block message
                elif self.action == MCPAction.DELAY:
                    # Calculate delay needed
                    oldest_message = min(self.user_history[user_id])
                    delay_needed = self.time_window - (current_time - oldest_message)
                    if delay_needed > 0:
                        logger.info(f"Delaying message from user {user_id} by {delay_needed:.2f} seconds")
                        await asyncio.sleep(delay_needed)
            
            # Add message to history
            self.user_history[user_id].append(current_time)
            return message
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return message

# Register the adapter
mcp_registry.register_rule("rate_limiter", RateLimiterAdapter) 