"""Staff API endpoints."""

from typing import Optional, Dict, Any, List, AsyncGenerator
from fastapi import APIRouter, Depends, HTTPException, status, Path, Body, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import logging
import json
import asyncio

from onexbots.shared import ConversationService
from onexbots.shared.services.virtual_staff_service import VirtualStaffService
from onexbots.shared.models.api import (
    ChatRequest, 
    ChatResponse, 
    ChatAsyncRequest,
    ChatAsyncResponse, 
    ErrorResponse
)
from onexbots.shared.config import settings
from app.services.agent.staff_agent import StaffAgent
from onexbots.shared.services.virtual_staff_service import get_virtual_staff

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/staff",
    tags=["staff"],
    responses={
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"model": ErrorResponse},
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid request parameters",
            "model": ErrorResponse,
        },
    },
)


class MessageResponse(BaseModel):
    """Response model for conversation messages."""

    success: bool = Field(True, description="Whether the request was successful")
    data: Dict[str, Any] = Field(..., description="Paginated conversation messages")
    message: Optional[str] = Field(
        None, description="Optional message about the response"
    )


class StreamChatResponse(BaseModel):
    """Response model for streaming chat responses."""

    content: str = Field(..., description="Content of the streaming response")
    conversation_id: Optional[str] = Field(None, description="ID of the conversation")
    done: bool = Field(False, description="Whether this is the final message")


@router.post(
    "/{staff_id}/chat",
    response_model=ChatResponse,
    status_code=status.HTTP_200_OK,
    summary="Chat with a staff member",
    description="Initiates or continues a conversation with an AI staff member",
    responses={
        status.HTTP_200_OK: {
            "description": "Successful chat response",
            "model": ChatResponse,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid request parameters",
            "model": ErrorResponse,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Staff member not found",
            "model": ErrorResponse,
        },
    },
)
async def chat_with_staff(
    staff_id: str = Path(..., description="Unique identifier of the staff member"),
    request: ChatRequest = Body(..., description="Chat request details"),
    virtual_staff_service: VirtualStaffService = Depends(VirtualStaffService),
):
    """
    Chat with a staff member using AI.

    This endpoint allows users to:
    - Start a new conversation with a staff member
    - Continue an existing conversation
    - Get AI-powered responses based on staff configuration

    The endpoint requires either:
    - An existing user_id, or
    - A phone number and name to create a new user
    """
    try:
        # Get staff configuration
        staff = get_virtual_staff(staff_id)
        if not staff:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Staff member {staff_id} not found",
            )

        # Initialize staff agent with configuration
        staff_agent = StaffAgent(staff=staff["data"])

        # Process chat using staff agent
        result = await staff_agent.process_chat(
            message=request.message,
            staff_id=staff_id,
            user_id=request.user_id,
            phone_number=request.phone_number,
            name=request.name,
        )

        return ChatResponse(
            response=result["response"], conversation_id=result["conversation_id"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing chat: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing chat request: {str(e)}",
        )

@router.post("/{staff_id}/chat/async",
             response_model=ChatAsyncResponse,
             status_code=status.HTTP_202_ACCEPTED,
             summary="Chat with a staff member asynchronously",
             description="Initiates an asynchronous conversation with an AI staff member and returns immediately with a request ID",
             responses={
                 status.HTTP_202_ACCEPTED: {
                     "description": "Request accepted for processing",
                     "model": ChatAsyncResponse,
                 },
                 status.HTTP_400_BAD_REQUEST: {
                     "description": "Invalid request parameters",
                     "model": ErrorResponse,
                 },
                 status.HTTP_404_NOT_FOUND: {
                     "description": "Staff member not found",
                     "model": ErrorResponse,
                 },
             })
async def chat_with_staff_async(
    staff_id: str = Path(..., description="Unique identifier of the staff member"),
    request: ChatAsyncRequest = Body(..., description="Chat request details"),
    virtual_staff_service: VirtualStaffService = Depends(VirtualStaffService),
):
    """
    Chat with a staff member asynchronously.
    Returns immediately with a request ID while processing continues in the background.
    
    This endpoint requires:
    - connection_id: For tracking the chat session
    - external_user_id: For identifying the external user
    """
    try:
        # Get staff configuration
        staff = get_virtual_staff(staff_id)
        if not staff:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Staff member {staff_id} not found",
            )

        # Initialize staff agent with configuration
        staff_agent = StaffAgent(staff=staff["data"])

        # Invoke asynchronous processing
        await staff_agent.process_chat_async(
            message=request.message,
            staff_id=staff_id,
            external_user_id=request.external_user_id,
            phone_number=request.phone_number,
            name=request.name,
            connection_id=request.connection_id,
            channel_id=request.channel_id
        )
        
        return ChatAsyncResponse(
            message="Request accepted for processing"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in async chat request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing async chat request: {str(e)}",
        )


@router.post(
    "/{staff_id}/chat/stream",
    response_class=StreamingResponse,
    status_code=status.HTTP_200_OK,
    summary="Stream chat with a staff member",
    description="Initiates or continues a streaming conversation with an AI staff member",
    responses={
        status.HTTP_200_OK: {
            "description": "Streaming chat response",
            "content": {
                "text/event-stream": {
                    "example": {
                        "content": "Hello, how can I help you?",
                        "conversation_id": "123",
                        "done": False,
                    }
                }
            },
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid request parameters",
            "model": ErrorResponse,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Staff member not found",
            "model": ErrorResponse,
        },
    },
)
async def stream_chat_with_staff(
    staff_id: str = Path(..., description="Unique identifier of the staff member"),
    request: ChatRequest = Body(..., description="Chat request details"),
    virtual_staff_service: VirtualStaffService = Depends(VirtualStaffService),
    conversation_service: ConversationService = Depends(ConversationService),
):
    """
    Stream chat with a staff member using AI.

    This endpoint allows users to:
    - Start a new streaming conversation with a staff member
    - Continue an existing conversation with streaming responses
    - Get real-time AI-powered responses based on staff configuration

    The endpoint requires either:
    - An existing user_id, or
    - A phone number and name to create a new user
    """
    try:
        # Get staff configuration
        staff = get_virtual_staff(staff_id)
        if not staff:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Staff member {staff_id} not found",
            )

        # Initialize staff agent with configuration
        staff_agent = StaffAgent(staff=staff["data"])

        async def generate_stream() -> AsyncGenerator[str, None]:
            try:
                # Process chat using staff agent with streaming
                async for chunk in staff_agent.process_chat_stream(
                    message=request.message,
                    staff_id=staff_id,
                    user_id=request.user_id,
                    phone_number=request.phone_number,
                    name=request.name,
                ):
                    # Create response object
                    response = StreamChatResponse(
                        content=chunk.get("content", ""),
                        conversation_id=chunk.get("conversation_id"),
                        done=chunk.get("done", False),
                    )
                    yield f"data: {json.dumps(response.model_dump())}\n\n"
                logger.debug("Chat stream endpoint finished successfully")
            except Exception as e:
                error_response = StreamChatResponse(
                    content=f"Error: {str(e)}", done=True
                )
                yield f"data: {json.dumps(error_response.model_dump())}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing streaming chat: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing streaming chat request: {str(e)}",
        )


@router.get(
    "/conversations/{conversation_id}/messages",
    response_model=MessageResponse,
    status_code=status.HTTP_200_OK,
    summary="Get conversation messages",
    description="Retrieves all messages for a specific conversation",
    responses={
        status.HTTP_200_OK: {
            "description": "List of conversation messages",
            "model": MessageResponse,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Conversation not found",
            "model": ErrorResponse,
        },
    },
)
async def get_conversation_messages(
    conversation_id: str = Path(
        ..., description="Unique identifier of the conversation"
    ),
    conversation_service: ConversationService = Depends(ConversationService),
):
    """
    Get all messages for a conversation.

    This endpoint allows users to:
    - Retrieve the complete message history for a conversation
    - See both user messages and staff responses
    - Access message metadata like timestamps and roles
    - Get paginated results with total count and page information
    """
    try:
        # Get conversation messages
        messages = conversation_service.get_messages(conversation_id)

        if messages is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Conversation {conversation_id} not found",
            )

        return MessageResponse(
            success=True,
            data=messages,
            message="Successfully retrieved conversation messages",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving conversation messages: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving conversation messages: {str(e)}",
        )
