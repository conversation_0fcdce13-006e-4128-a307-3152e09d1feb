class BaseSystemPrompt:
    def __init__(self, staff, metadata):
        self.staff = staff
        self.metadata = metadata
        self.LANGUAGE_CODE_TO_NAME = {
            "vi": "Vietnamese",
            "en": "English",
            "ja": "Japanese",
            "ko": "Korean",
            "zh": "Chinese",
            "fr": "French",
            "de": "German",
            "es": "Spanish",
        }

    def build(self):
        raise NotImplementedError("Subclasses must implement build()")

    def convert_language_code_to_name(self, language_code):
        return self.LANGUAGE_CODE_TO_NAME.get(language_code, language_code)
