import logging
from onexbots.shared.services.knowledge_service import KnowledgeService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    # Initialize knowledge service
    knowledge_service = KnowledgeService()

    # Test list knowledge
    knowledge_list = knowledge_service.list_knowledge()
    if knowledge_list:
        logger.info("Successfully retrieved knowledge list")
        print(knowledge_list)
    else:
        logger.error("Failed to get knowledge list")

    # Example: Create knowledge from text
    text_knowledge = knowledge_service.create_knowledge_from_text(
        text="This is a test knowledge item",
        metadata={"source": "test", "type": "text"}
    )
    if text_knowledge:
        logger.info("Successfully created knowledge from text")
        print(text_knowledge)
    else:
        logger.error("Failed to create knowledge from text")

    # Test updating knowledge status
    knowledge_id = 'b1c846bf-4a63-41ff-bbbd-e65495c4ff10'
    company_id = '9e61d187-426a-45ec-914d-7aea8ca7d42d'
    status_update = knowledge_service.update_knowledge_status(
        knowledge_id=knowledge_id,
        status='PROCESSING',
        company_id=company_id
    )
    
    if status_update:
        logger.info(f"Successfully updated knowledge status for ID {knowledge_id}")
        print(status_update)
    else:
        logger.error(f"Failed to update knowledge status for ID {knowledge_id}")

if __name__ == "__main__":
    main()
