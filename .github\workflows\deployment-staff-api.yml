name: Deploy staff

on:
    workflow_call:
        inputs:
            service:
                required: true
                type: string
            SLACK_DEV_USER_IDS:
                required: true
                type: string
        secrets:
            AWS_ACCESS_KEY_ID:
                required: true
            AWS_SECRET_ACCESS_KEY:
                required: true
            AWS_REGION:
                required: true
            SLACK_BOT_TOKEN:
                required: true
            HOST:
                required: true
            USERNAME:
                required: true
            PASSWORD:
                required: true
            AWS_ACCOUNT_ID:
                required: true

jobs:
    deploy-staff-api:
        runs-on: [self-hosted, odoo1]
        timeout-minutes: 60
        steps:
            - name: 🔄 Checkout Repo
              uses: actions/checkout@v4

            - name: ⚙️ Set environment
              run: |
                  echo "GITHUB_REF_NAME=${GITHUB_REF_NAME}" >> $GITHUB_ENV

                  if [[ "$GITHUB_REF_NAME" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    echo "DEPLOY_ENV=Production" >> $GITHUB_ENV
                    echo "ENV_FILE=.env.prod" >> $GITHUB_ENV
                    echo "DEPLOY_URL=https://agent.onexbots.com" >> $GITHUB_ENV
                  else
                    echo "DEPLOY_ENV=Development" >> $GITHUB_ENV
                    echo "ENV_FILE=.env" >> $GITHUB_ENV
                    echo "DEPLOY_URL=https://dev-agent.onexbots.com" >> $GITHUB_ENV
                  fi

            - name: 🔐 Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v2
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ${{ secrets.AWS_REGION }}

            - name: 🐳 Build & Push Docker Images to AWS ECR
              working-directory: ./onexbots
              env:
                  AWS_REGION: ${{ secrets.AWS_REGION }}
                  SERVICE: ${{inputs.service}}
                  TAG: ${{ github.ref_name }}
                  AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
              run: |
                  set -e

                  echo "🔧 Using tag: $TAG"

                  # 🔐 Login vào AWS ECR
                  aws ecr get-login-password --region $AWS_REGION | \
                    docker login --username AWS \
                    --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com

                  # === 🔨 Build & Push: staff_api ===
                  echo "🚀 Building $SERVICE..."
                  docker build -t $SERVICE:$TAG -f $SERVICE/Dockerfile .
                  docker tag $SERVICE:$TAG ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com/$SERVICE:$TAG
                  docker push ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.$AWS_REGION.amazonaws.com/$SERVICE:$TAG

                  echo "✅ Docker images pushed successfully!"

            - name: 🚀 Deploy to Server bots
              uses: appleboy/ssh-action@master
              with:
                  host: ${{ secrets.HOST }}
                  username: ${{ secrets.USERNAME }}
                  password: ${{ secrets.PASSWORD }}
                  port: 22
                  envs: AWS_ACCESS_KEY_ID,AWS_SECRET_ACCESS_KEY,AWS_REGION,AWS_ACCOUNT_ID,TAG,DEPLOY_ENV,SERVICE
                  script: |
                      set -e

                      echo "🔐 AWS ECR login..."
                      aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com"

                      cd onexbots_services/onexbots-images

                      ECR_REGISTRY="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com"
                      STAFF_API_IMAGE="$ECR_REGISTRY/staff_api:$TAG"

                      # Xác định ENV_FILE trước khi dùng
                      if [[ "$DEPLOY_ENV" == "Production" ]]; then
                        ENV_FILE=".env.prod"
                      else
                        ENV_FILE=".env"
                      fi

                      echo "🔁 Update $ENV_FILE..."
                      for file in "$ENV_FILE"; do
                        grep -q '^STAFF_API_IMAGE=' "$file" && sed -i "s|^STAFF_API_IMAGE=.*|STAFF_API_IMAGE=$STAFF_API_IMAGE|" "$file" || echo "STAFF_API_IMAGE=$STAFF_API_IMAGE" >> "$file"
                      done

                      echo "📥 Pull images..."
                      docker pull "$STAFF_API_IMAGE"

                      echo "🔄 Restarting services..."
                      if [[ "$DEPLOY_ENV" == "Production" ]]; then
                        docker compose -f docker-compose.prod.yml restart staff_api_prod
                      else
                        docker compose restart staff_api
                      fi

              env:
                  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  AWS_REGION: ${{ secrets.AWS_REGION }}
                  AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
                  TAG: ${{ github.ref_name }}
                  SERVICE: ${{inputs.service}}

            - name: 📢 Send Success Notification
              if: success()
              env:
                  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
                  DEPLOY_ENV: ${{ env.DEPLOY_ENV }}
                  GITHUB_REF_NAME: ${{ github.ref_name }}
                  DEPLOY_URL: ${{ env.DEPLOY_URL }}
                  SLACK_DEV_USER_IDS: ${{ inputs.SLACK_DEV_USER_IDS }}
                  GITHUB_ACTOR: ${{ github.actor }}
                  PR_BODY: ${{ github.event.pull_request.body }}
                  PR_AUTHOR: ${{ github.event.pull_request.user.login }}
                  PR_TITLE: ${{ github.event.pull_request.title }}
                  PR_NUMBER: ${{ github.event.pull_request.number }}
                  PR_URL: ${{ github.event.pull_request.html_url }}
                  JIRA_LINK: https://onexbots.atlassian.net/browse/
                  COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
                  COMMIT_URL: ${{ github.event.head_commit.url }}
                  SERVICE: ${{inputs.service}}
                  TAG: ${{ github.ref_name }}
              run: |
                  if [ -n "$SLACK_DEV_USER_IDS" ]; then
                    MENTIONS=$(echo "$SLACK_DEV_USER_IDS" | tr ',' '\n' | sed 's/.*/<@&>/' | paste -sd' ' -)
                  else
                    MENTIONS=""
                  fi

                  # fallback author nếu không có pull_request
                  AUTHOR=${PR_AUTHOR:-$GITHUB_ACTOR}

                  FORMATTED_TITLE=$(echo "$PR_TITLE" | sed -E "s#(OWS-[0-9]+)#<${JIRA_LINK}\1|\1>#g")

                  if [ -n "$PR_NUMBER" ] && [ -n "$PR_URL" ]; then
                    PR_TEXT="*Pull Request:*\n<$PR_URL|#${PR_NUMBER}> - $FORMATTED_TITLE"
                  elif [ -n "$COMMIT_MESSAGE" ] && [ -n "$COMMIT_URL" ]; then
                    COMMIT_SHORT_SHA=$(echo "$COMMIT_URL" | grep -o '[a-f0-9]\{7\}$')
                    PR_TEXT="*Commit:*\n<$COMMIT_URL|$COMMIT_SHORT_SHA> - $COMMIT_MESSAGE"
                  else
                    PR_TEXT="No PR or commit info available."
                  fi

                  # Escape double quotes and backslashes in PR_TEXT for JSON
                  PR_BLOCK=$(printf '%s' "$PR_TEXT" | sed 's/\\/\\\\/g; s/"/\\"/g; s/$/\\n/' | tr -d '\n')

                  curl -X POST https://slack.com/api/chat.postMessage \
                  -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
                  -H "Content-type: application/json" \
                  --data "$(cat <<EOF
                  {
                    "channel": "#prd-onexbots-deployment",
                    "text": "✅ Deployment Successful on $DEPLOY_ENV (\`$GITHUB_REF_NAME\`)",
                    "blocks": [
                      {
                        "type": "header",
                        "text": { "type": "plain_text", "text": "✅ Deployment $SERVICE $TAG Successful!", "emoji": true }
                      },
                      {
                        "type": "section",
                        "fields": [
                          { "type": "mrkdwn", "text": "*Author:*\n$AUTHOR" },
                          { "type": "mrkdwn", "text": "*Deployment URL:*\n<$DEPLOY_URL|View Deployment>" }
                        ]
                      },
                      {
                        "type": "context",
                        "elements": [
                          { "type": "mrkdwn", "text": "*Details:*\n• $PR_BLOCK" }
                        ]
                      },
                      {
                        "type": "context",
                        "elements": [
                          { "type": "mrkdwn", "text": "👀 *Please review the code changes and provide feedback!*\ncc: $MENTIONS" }
                        ]
                      }
                    ]
                  }
                  EOF
                  )"

            - name: 📢 Send Failure Notification
              if: failure()
              env:
                  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
                  DEPLOY_ENV: ${{ env.DEPLOY_ENV }}
                  GITHUB_REF_NAME: ${{ github.ref_name }}
                  GITHUB_SERVER_URL: ${{ github.server_url }}
                  GITHUB_REPOSITORY: ${{ github.repository }}
                  GITHUB_RUN_ID: ${{ github.run_id }}
                  SLACK_DEV_USER_IDS: ${{ vars.SLACK_DEV_USER_IDS }}
                  GITHUB_ACTOR: ${{ github.actor }}
                  PR_BODY: ${{ github.event.pull_request.body }}
                  PR_AUTHOR: ${{ github.event.pull_request.user.login }}
                  PR_TITLE: ${{ github.event.pull_request.title }}
                  PR_NUMBER: ${{ github.event.pull_request.number }}
                  PR_URL: ${{ github.event.pull_request.html_url }}
                  JIRA_LINK: https://onexbots.atlassian.net/browse/
                  COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
                  COMMIT_URL: ${{ github.event.head_commit.url }}
                  SERVICE: ${{inputs.service}}
              run: |
                  if [ -n "$SLACK_DEV_USER_IDS" ]; then
                    MENTIONS=$(echo "$SLACK_DEV_USER_IDS" | tr ',' '\n' | sed 's/.*/<@&>/' | paste -sd' ' -)
                  else
                    MENTIONS=""
                  fi

                  AUTHOR=${PR_AUTHOR:-$GITHUB_ACTOR}

                  FORMATTED_TITLE=$(echo "$PR_TITLE" | sed -E "s#(OWS-[0-9]+)#<${JIRA_LINK}\1|\1>#g")

                  if [ -n "$PR_NUMBER" ] && [ -n "$PR_URL" ]; then
                    PR_TEXT="*Pull Request:*\n<$PR_URL|#${PR_NUMBER}> - $FORMATTED_TITLE"
                  else
                    COMMIT_SHORT_SHA=$(echo "$COMMIT_URL" | grep -o '[a-f0-9]\{7\}$')
                    PR_TEXT="*Commit:*\n<$COMMIT_URL|$COMMIT_SHORT_SHA> - $COMMIT_MESSAGE"
                  fi

                  # Escape for JSON
                  PR_BLOCK=$(printf '%s' "$PR_TEXT" | sed 's/\\/\\\\/g; s/"/\\"/g; s/$/\\n/' | tr -d '\n')

                  curl -X POST https://slack.com/api/chat.postMessage \
                    -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
                    -H "Content-type: application/json" \
                    --data @- <<EOF
                  {
                    "channel": "#prd-onexbots-deployment",
                    "text": "❌ Deployment Failed on $DEPLOY_ENV (\`$GITHUB_REF_NAME\`)",
                    "blocks": [
                      {
                        "type": "header",
                        "text": { "type": "plain_text", "text": "❌ Deployment $SERVICE Failed!", "emoji": true }
                      },
                      {
                        "type": "section",
                        "fields": [
                          { "type": "mrkdwn", "text": "*Author:*\n$AUTHOR" },
                          { "type": "mrkdwn", "text": "*Branch:*\n\`$GITHUB_REF_NAME\`" }
                        ]
                      },
                      {
                        "type": "section",
                        "fields": [
                          { "type": "mrkdwn", "text": "*Details:*\n• $PR_BLOCK" }
                        ]
                      },
                      {
                        "type": "context",
                        "elements": {
                          "type": "mrkdwn",
                          "text": "*Workflow Logs:*\n<$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID|View Logs>"
                        }
                      },
                      {
                        "type": "context",
                        "elements": [
                          { "type": "mrkdwn", "text": "⚠️ *Please check the logs and fix the issue.*\ncc: $MENTIONS" }
                        ]
                      }
                    ]
                  }
                  EOF
