"""A tool for getting the current system time in ISO 8601 format."""

from langchain_core.tools import tool
import logging
from datetime import datetime, timezone
from ..registry import ToolMetadata, tool_registry
from langchain_core.runnables import RunnableConfig

# Configure logger
logger = logging.getLogger(__name__)


@tool
def timer(config: RunnableConfig = None) -> str:
    """Get the current system time in ISO 8601 format (UTC)."""
    try:
        now = datetime.now(timezone.utc)
        iso_time = now.isoformat()
        return f"Current time (UTC): {iso_time}"
    except Exception as e:
        logger.error(f"Error in timer tool: {str(e)}")
        return "Error: Could not get current time."


# Register the tool with metadata
timer_tool_metadata = ToolMetadata(
    name="timer",
    description="Useful for getting the current system time in ISO 8601 format (UTC)",
    tags=["timer", "time", "current time", "iso8601"],
)
tool_registry.register_tool(timer, timer_tool_metadata)
logger.debug("Registered timer tool")

