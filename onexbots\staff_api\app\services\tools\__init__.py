"""
Tools package for LangChain tools.

This package provides a collection of tools that can be used with LangChain agents.
Each tool is implemented as a class that inherits from langchain_core.tools.BaseTool.
"""

from .registry import ToolManager, ToolMetadata, tool_registry
from .system_tools.product_retriever import retrieve_products
from .system_tools.timer_tool import timer

# Import tools to ensure they get registered

__all__ = ["ToolManager", "ToolMetadata", "tool_registry", "retrieve_products", "timer"]
