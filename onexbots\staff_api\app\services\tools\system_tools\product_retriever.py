from typing import List, Optional, Dict, Any
from onexbots.shared.services.embedding_service import EmbeddingService
from langchain_core.tools import tool
import logging
import json
from ..registry import ToolMetadata, tool_registry
from langchain_core.runnables import RunnableConfig
import re

logger = logging.getLogger(__name__)


class ProductRetrieverTool:
    """
    Tool to retrieve product information from the embedded product database.
    Usage: Call with a query string (product name, SKU, or description keywords).
    Returns a list of the most relevant products.
    """

    name = "product_retriever"
    description = "Retrieve product information from the embedded product database by name, SKU, or description keywords."

    def __init__(
        self,
        embedding_service: Optional[EmbeddingService] = None,
        company_id: Optional[str] = None,
    ):
        from onexbots.shared.config import settings

        self.embedding_service = embedding_service or EmbeddingService(settings)
        self.company_id = company_id

    async def __call__(
        self,
        query: str,
        top_k: int = 3,
        record_type: str = "product",
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Retrieve the most relevant products for the given query.
        """
        retriever = await self.embedding_service.product_retriever(
            company_id=self.company_id, record_type=record_type, filters=filters
        )
        docs = await retriever.aget_relevant_documents(query)
        docs = docs[:top_k]
        results = []
        for doc in docs:
            results.append(
                {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                }
            )
        return results


def extract_field(content: str, field: str) -> Optional[str]:
    match = re.search(rf"^{field}: ?(.+)$", content, re.MULTILINE)
    if match:
        return match.group(1).strip()
    match = re.search(rf"{field}: ?(.+)$", content)
    if match:
        return match.group(1).strip()
    return None


def extract_variants(content: str) -> list:
    variant_blocks = re.split(r"^variants\[\d+\]:", content, flags=re.MULTILINE)[1:]
    variants = []
    for block in variant_blocks:
        name = extract_field(block, "name")
        sku = extract_field(block, "sku")
        option1 = extract_field(block, "option1")
        option2 = extract_field(block, "option2")
        price_matches = re.findall(r"price: ([0-9]+)", block)
        images = re.findall(r"url: (https?://\S+)", block)
        inventory = extract_field(block, "inventories")
        variants.append(
            {
                "name": name,
                "sku": sku,
                "option1": option1,
                "option2": option2,
                "prices": price_matches,
                "images": images,
                "inventory": inventory,
            }
        )
    return variants


def extract_inventory(content: str) -> Optional[str]:
    return extract_field(content, "inventories")


def format_product_markdown(
    summary: dict, content: str, extra_fields: Optional[list] = None
) -> str:
    images_md = "\n".join([f"![product image]({url})" for url in summary["images"]])
    variants_md = ""
    if summary.get("variants"):
        for v in summary["variants"]:
            v_images = "\n".join(
                [f"![variant image]({url})" for url in v.get("images", [])]
            )
            variants_md += f"\n- **Variant Name:** {v.get('name', '')} | **SKU:** {v.get('sku', '')} | **Option1:** {v.get('option1', '')} | **Option2:** {v.get('option2', '')} | **Prices:** {', '.join(v.get('prices', []))} | **Inventory:** {v.get('inventory', '')}\n{v_images}"
    inventory_md = summary.get("inventory") or ""
    extra_md = ""
    if extra_fields:
        for field in extra_fields:
            value = extract_field(content, field)
            if value:
                extra_md += f"\n**{field}:** {value}"
    return f"""
**Name:** {summary.get('name', '')}  
**Description:** {summary.get('description', '')}  
**SKU:** {summary.get('sku', '')}  
**ID:** {summary.get('id', '')}  
**Prices:** {', '.join(summary.get('prices', []))}  
**Inventory:** {inventory_md}  
{images_md}
**Variants:**{variants_md if variants_md else ' None'}{extra_md}
""".strip()


@tool
async def retrieve_products(
    query: str,
    config: RunnableConfig = None,
    top_k: int = 3,
    extra_fields: Optional[list] = None,
) -> str:
    """Retrieve the most relevant products from the embedded product database by query (name, SKU, or description keywords). Optionally include extra fields."""
    try:
        company_id = None
        if config:
            company_id = config.get("configurable", {}).get("company_id")
        if not company_id:
            return "No relevant products found (missing company_id)."
        retriever = ProductRetrieverTool(company_id=company_id)
        products = await retriever(query, top_k=top_k)
        if not products:
            return "No relevant products found."
        product_md_blocks = []
        for item in products:
            content = item["content"]
            metadata = item["metadata"]
            summary = {
                "name": extract_field(content, "name") or metadata.get("name"),
                "description": extract_field(content, "description"),
                "sku": extract_field(content, "sku") or metadata.get("sku"),
                "id": metadata.get("id"),
                "images": re.findall(r"url: (https?://\S+)", content),
                "prices": re.findall(r"price: ([0-9]+)", content),
                "inventory": extract_inventory(content),
                "variants": extract_variants(content),
            }
            product_md_blocks.append(
                format_product_markdown(summary, content, extra_fields=extra_fields)
            )
        return "\n---\n".join(product_md_blocks)
    except Exception as e:
        logger.error(f"Error in retrieve_products tool: {str(e)}")
        return "No relevant products found."


# Register the tool with metadata
product_retriever_tool_metadata = ToolMetadata(
    name="product_retriever",
    description="Useful for retrieving relevant products from the embedded product database by query (name, SKU, or description keywords)",
    tags=["products", "product", "retrieve products"],
)
tool_registry.register_tool(retrieve_products, product_retriever_tool_metadata)
logger.debug("Registered product_retriever tool")

# Example usage (async):
# retriever = ProductRetrieverTool(company_id="your_company_id")
# products = await retriever("red t-shirt size M")
# print(products)
