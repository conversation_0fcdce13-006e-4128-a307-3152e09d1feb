name: 🚀 Release on Dev

on:
    push:
        branches:
            - dev
            - main

jobs:
    release:
        runs-on: self-hosted
        permissions:
            contents: write
            issues: write
            pull-requests: write

        steps:
            - name: 📥 Checkout
              uses: actions/checkout@v3
              with:
                  fetch-depth: 0

            - name: 🧰 Setup Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: 20

            - name: 🧪 Init and Install semantic-release
              run: |
                  npm init -y
                  npm install --save-dev semantic-release \
                    @semantic-release/commit-analyzer \
                    @semantic-release/release-notes-generator \
                    @semantic-release/github \
                    @semantic-release/git \
                    @semantic-release/exec

            - name: 🚀 Run semantic-release
              env:
                  GH_TOKEN: ${{ secrets.GH_PAT }}
              run: npx semantic-release

            - name: ⏱ Wait for GitHub to register tag
              run: sleep 10

            - name: 📦 Read release version
              id: get_version
              run: |
                  echo "version=$(cat release-version.txt)" >> $GITHUB_OUTPUT

            - name: 🔁 Trigger Server Deployment Workflow
              if: success()
              env:
                  GH_PAT: ${{ secrets.GH_PAT }}
              run: |
                  curl -X POST https://api.github.com/repos/OneXApis/onexbots_services/actions/workflows/deployment-ci.yaml/dispatches \
                    -H "Accept: application/vnd.github+json" \
                    -H "Authorization: Bearer $GH_PAT" \
                    -d '{"ref":"refs/tags/v${{ steps.get_version.outputs.version }}"}'
