ONEXBOTS Microservices Project – Detailed Instruction Set
1. Project Overview
Project Name: ONEXBOTS
Technology Stack:

Programming Language: Python

Web Framework: FastAPI (for building RESTful APIs)

AI Library: LangChain (for building and integrating AI agents)

Database: PostgreSQL (primarily for storing embedding data and powering AI searches)

Message/Event Processing: AWS SQS with S3 events (for handling file ingestion in the RAG pipeline)

Monitoring & Visualization: Prometheus (for metrics) and Grafana (for dashboards)

Configuration Management: Python-dotenv to load environment-specific variables

Deployment: Containerized services orchestrated with Docker Compose for local and virtual machine deployments

Package Management: uv as the package manager for installing dependencies and running project commands

2. Architectural Components
2.1. Staff Microservice (ONEXBOTS Staff API)
Endpoints and Their Responsibilities
staff/{staff_id}/chat Endpoint:

Purpose: Initiates a conversation with the AI agent.

User Identification:

Primary: A provided user_id is used directly.

Fallback: If no user_id is provided, the user must supply a phone number and name. The system will verify these details against the ONEXSTAFFS API, creating a new user and generating a user_id if one does not exist.

Conversation Flow:

Retrieve staff-specific configurations from the ONEXSTAFFS API using the staff_id (these configurations include LangChain initialization parameters, tool enabling settings, and MCP-related configurations).

Initialize a LangChain-based AI agent using the retrieved configuration.

Begin and manage the conversation session.

Conversation Logging: All conversation data should be forwarded via a POST request to the ONEXSTAFFS API endpoint conversation/{conversation_id} for centralized storage.

staff/{staff_id}/embed Endpoint:

Purpose: Generates an embeddable script that external websites can use to integrate the staff’s configuration.

Processing:

Retrieve configuration data by calling the GET staff/{staff_id} endpoint on the ONEXSTAFFS API.

Generate and return an embed script using the obtained configuration.

Additional API Documentation
Swagger Integration:

Auto-generate Swagger documentation for all endpoints, detailing request/response schemas and security requirements.

2.2. RAG (Retrieval Augmented Generation) Pipeline
Workflow Steps:

Message Consumption:

Monitor an AWS SQS queue for messages triggered by S3 events (indicating file uploads or modifications).

Status Update:

Immediately set the file’s knowledge status to processing upon receiving a message.

File Processing:

Process supported file types, including PDFs, text files, DOCs, and a special .url file that contains URLs for crawling.

Implement file format validation to ensure only supported formats are processed.

Summarization:

Generate a concise summary of the file content.

Embedding Generation:

Generate embedding vectors using appropriate algorithms, enabling efficient AI searches via LangChain.

Error Handling:

In case of processing failures or embedding generation issues, update the knowledge status to failed and move the message to a dead-letter queue.

Finalization:

Once processing is complete, update the file’s knowledge status to ready.

2.3. Database Integration (PostgreSQL)
Primary Role:

Store embedding data and support LangChain-based AI search functionalities.

Note:

All other configuration and conversation data remain in the ONEXSTAFFS API.

Schema Considerations:

Optimize database schema for fast similarity searches; consider using vector indexing extensions (such as PGVector).

2.4. Observability and Monitoring
Prometheus Metrics:

API response times and request throughput

Error rates and exception counts

SQS message processing times

System resource utilization (CPU, memory)

Database query performance for embedding operations

Grafana Integration:

Set up custom Grafana dashboards based on Prometheus metrics, with room for future expansion as new monitoring requirements arise.

2.5. Shared Service: ONEXSTAFFS API Integration
Responsibilities:

Develop a reusable module/service that abstracts interactions with the ONEXSTAFFS API.

Authentication & Security:

Leverage AWS Cognito and API Gateway for secure service communication.

Apply best practices for inter-service communication (including mutual TLS or signed requests when required).

Configuration Retrieval:

Provide methods to fetch staff configurations (including LangChain parameters, tool settings, and MCP configurations).

Best Practices:

Use libraries such as uv (as the package manager will also control dependency management) in conjunction with standard HTTP clients like httpx or requests with built-in retries and timeouts.

Optionally implement caching for configuration data where applicable.

2.6. Environment Configuration Management
Supported Environments: Test, Development, Staging, Production
Key Variables:

API endpoints

Database URIs

Authentication tokens and other secrets

Implementation:

Use Python-dotenv to load environment-specific variables.

Maintain distinct .env files (e.g., .env.test, .env.dev, .env.staging, .env.prod).

Implement a configuration loader that automatically selects the appropriate file based on an environment variable (e.g., ENVIRONMENT).

2.7. General Project Management and Deployment
Project Structure:

Use a monorepo structure, with clear submodules for:

The Staff API

The RAG pipeline

Shared ONEXSTAFFS API integration components

Deployment with Docker Compose:

Containerization:

Package each service as a Docker image.

Docker Compose Orchestration:

Create a docker-compose.yml file to define and run all multi-container services.

The Compose file should include:

Definitions for the staff API, RAG pipeline, PostgreSQL, Prometheus, and Grafana.

Injection of environment variables (linking to appropriate .env files).

Networking configurations to ensure secure inter-container communications.

Volume mappings for persistent data (e.g., PostgreSQL storage).

Target Deployment:

Utilize Docker Compose for local development and deployments on virtual machines.

Using uv for Package Management:

All dependencies and project scripts will be managed with uv.

uv should be used in Dockerfiles and local development scripts to install packages, manage virtual environments, and run commands.

Ensure that uv configuration files are included in the repository (for example, uv.lock or a similar configuration file) to ensure consistent dependency management across environments.

2.8. Additional Functionality
Security Measures:

Rate Limiting:

Enforce rate limits on API endpoints to prevent abuse.

API Key Management:

Utilize API keys for both external and internal communications.

TLS:

Ensure that all endpoints are secured over HTTPS.

Logging & Error Tracking:

Logging:

Implement structured logging using Python’s logging module (with JSON formatting for centralized log collection).

Error Tracking:

Integrate an error tracking service (such as Sentry) to capture real-time error alerts and detailed stack traces.

Suggestions:

Consider centralized logging solutions (such as an ELK/EFK stack) for aggregating logs from all services.