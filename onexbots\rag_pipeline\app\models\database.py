import logging
import asyncio
from typing import Optional
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
from onexbots.shared.config import settings
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

# Configure logging
logger = logging.getLogger(__name__)

# Database connection settings
MAX_RETRIES = 3
RETRY_DELAY = 5  # seconds

# Create database URL
DATABASE_URL = f"postgresql+asyncpg://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"

# Create async engine
engine = create_async_engine(
    DATABASE_URL,
    poolclass=NullPool,
    echo=settings.DEBUG,
)

# Create async session factory
async_session = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
)

async def get_db() -> AsyncSession:
    """Get a database session."""
    try:
        async with async_session() as session:
            yield session
    except SQLAlchemyError as e:
        logger.error(f"Database session error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in database session: {str(e)}")
        raise

async def _test_connection() -> bool:
    """Test database connection."""
    try:
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
            return True
    except Exception as e:
        logger.error(f"Database connection test failed: {str(e)}")
        return False

async def wait_for_db(max_retries: int = MAX_RETRIES, delay: int = RETRY_DELAY) -> bool:
    """Wait for database to become available."""
    for attempt in range(max_retries):
        try:
            if await _test_connection():
                logger.info("Database connection successful")
                return True
            logger.warning(f"Database connection attempt {attempt + 1}/{max_retries} failed. Retrying in {delay} seconds...")
            await asyncio.sleep(delay)
        except Exception as e:
            logger.warning(f"Database connection attempt {attempt + 1}/{max_retries} failed with error: {str(e)}")
            if attempt < max_retries - 1:
                await asyncio.sleep(delay)
    return False

# Initialize database
async def init_db():
    """Initialize database with required tables and extensions."""
    # Wait for database to become available
    if not await wait_for_db():
        logger.error("Failed to connect to database after maximum retries")
        raise ConnectionError("Failed to connect to database after maximum retries")

    try:
        async with engine.begin() as conn:
            # Create vector extension if not exists
            try:
                await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
                logger.info("Vector extension created or already exists")
            except SQLAlchemyError as e:
                logger.error(f"Failed to create vector extension: {str(e)}")
                raise

            # Create embeddings table
            try:
                await conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS embeddings (
                        id SERIAL PRIMARY KEY,
                        vector vector(1536),
                        metadata JSONB,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                logger.info("Embeddings table created or already exists")
            except SQLAlchemyError as e:
                logger.error(f"Failed to create embeddings table: {str(e)}")
                raise

            # Create index for vector similarity search
            try:
                await conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS embeddings_vector_idx 
                    ON embeddings 
                    USING ivfflat (vector vector_cosine_ops)
                    WITH (lists = 100)
                """))
                logger.info("Vector index created or already exists")
            except SQLAlchemyError as e:
                logger.error(f"Failed to create vector index: {str(e)}")
                raise

    except SQLAlchemyError as e:
        logger.error(f"Database initialization error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during database initialization: {str(e)}")
        raise 