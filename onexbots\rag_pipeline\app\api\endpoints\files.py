from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import logging

from app.services.file_processor import FileProcessor
from onexbots.shared.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)
file_processor = FileProcessor()

@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    Upload a file for processing and embedding generation.
    
    Args:
        file: The file to upload (PDF, TXT, DOCX, or XLSX)
        
    Returns:
        JSONResponse: Status of the upload and processing
    """
    try:
        result = await file_processor.process_file(file)
        return JSONResponse(content=result)
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/status/{file_id}")
async def get_file_status(file_id: str):
    """
    Get the processing status of a file.
    
    Args:
        file_id: The ID of the file to check
        
    Returns:
        JSONResponse: Current status of the file processing
    """
    try:
        status = await file_processor.get_file_status(file_id)
        return JSONResponse(content={"status": status})
    except Exception as e:
        logger.error(f"Error getting file status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 