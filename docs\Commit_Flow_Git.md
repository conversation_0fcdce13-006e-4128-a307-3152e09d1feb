# ✅ Quy tắc Commit theo Conventional Commit cho Python Project

Dự án này sử dụng **Commitizen + pre-commit** để kiểm tra message khi commit giống như Husky trong Node.js.

## 📦 C<PERSON><PERSON> loại commit được hỗ trợ:

| Loại commit | Mô tả ngắn                             | Ví dụ                                      |
|-------------|-----------------------------------------|---------------------------------------------|
| `feat`      | Thêm chức năng mới                      | `feat: thêm chức năng tìm kiếm`             |
| `fix`       | Sửa lỗi                                 | `fix: sửa lỗi phân trang sản phẩm`          |
| `chore`     | Việc lặt vặt không ảnh hưởng tới logic  | `chore: update thư viện`                    |
| `docs`      | Cập nhật tài liệu                       | `docs: cập nhật README`                     |
| `style`     | Format lại code                         | `style: chuẩn hóa khoảng trắng`             |
| `refactor`  | Thay đổi cấu trúc code                  | `refactor: gom controller thành base`       |
| `test`      | Thêm hoặc sửa test                      | `test: viết test cho auth service`          |
| `perf`      | Cải thiện hiệu năng                     | `perf: tối ưu truy vấn database`            |
| `ci`        | Thay đổi pipeline CI/CD                 | `ci: sửa pipeline build Docker`             |
| `revert`    | Hoàn tác một commit                     | `revert: revert commit abc123`              |

git commit -m "ci: add auto version bumping tag"



## 🧰 Cài đặt

```bash
pip install uv
```
```bash
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

## ⚙️ Setup
2. Cài hook Git:

```bash
pre-commit install --hook-type commit-msg
```

## 🧪 Kiểm tra

Thử commit sai:

```bash
git commit -m "update login"
```

👉 Kết quả: ❌ bị chặn vì sai chuẩn.

## ✅ Commit đúng cách

```bash
git commit -m "feat: thêm chức năng tìm kiếm"
```
```bash
git commit -m "ci: add auto version bumping tag"
```