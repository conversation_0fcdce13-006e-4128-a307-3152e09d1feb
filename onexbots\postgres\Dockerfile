ARG PG_MAJOR=17

FROM --platform=linux/amd64 postgres:$PG_MAJOR
ARG PG_MAJOR

RUN apt-get update && \
	apt-mark hold locales && \
	apt-get install -y --no-install-recommends git build-essential postgresql-server-dev-$PG_MAJOR && \
	git -c http.sslVerify=false clone --branch v0.8.0 https://github.com/pgvector/pgvector.git && \
	cd /pgvector && \
	make clean && \
	make OPTFLAGS="" && \
	make install && \
	mkdir /usr/share/doc/pgvector && \
	cp LICENSE README.md /usr/share/doc/pgvector && \
	rm -r /pgvector && \
	apt-get remove -y git build-essential postgresql-server-dev-$PG_MAJOR && \
	apt-get autoremove -y && \
	apt-mark unhold locales && \
	rm -rf /var/lib/apt/lists/*
