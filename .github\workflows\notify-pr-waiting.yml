name: ⏰ Notify PRs waiting too long (1h)

on:
    schedule:
        - cron: "0 2-12/2 * * 1-5"
    workflow_dispatch:

jobs:
    notify:
        runs-on: ubuntu-latest
        steps:
            - name: 📥 Get PRs opened
              id: waiting_prs
              uses: actions/github-script@v7
              with:
                  github-token: ${{ secrets.GH_PAT }}
                  script: |
                      const result = await github.paginate(github.rest.pulls.list, {
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        state: "open",
                        per_page: 100,
                      });

                      const now = new Date();

                      // Lấy giờ hiện tại theo giờ UTC và chuyển sang giờ Việt Nam (UTC+7)
                      const vnHour = (now.getUTCHours() + 7) % 24;

                      // Nếu đang trong khoảng 19h - 9h sáng thì không gửi thông báo
                      if (vnHour >= 19 || vnHour < 9) {
                        core.info("⏰ Outside notification hours (7PM - 9AM VN time). Skipping...");
                        core.setOutput("prs", "[]");
                        return;
                      }

                      const waiting = result.map(pr => {
                        const createdAt = new Date(pr.created_at);
                        const diffMinutes = Math.floor((now - createdAt) / 1000 / 60);

                        return {
                          title: pr.title,
                          number: pr.number,
                          url: pr.html_url,
                          user: pr.user.login,
                          created_at: createdAt.toISOString(),
                          minutes_waiting: diffMinutes
                        };
                      }).filter(pr =>
                        pr.minutes_waiting >= 240 
                      );

                      core.setOutput("prs", JSON.stringify(waiting));

            - name: 📣 Notify Slack
              if: ${{ steps.waiting_prs.outputs.prs != '[]' }}
              env:
                  SLACK_DEV_USER_IDS: ${{ vars.SLACK_DEV_USER_IDS }}
                  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
                  PRS_JSON: ${{ steps.waiting_prs.outputs.prs }}
              run: |
                  echo "$PRS_JSON" | jq -c '.[]' | while read -r pr; do
                    title=$(echo "$pr" | jq -r '.title')
                    url=$(echo "$pr" | jq -r '.url')
                    number=$(echo "$pr" | jq -r '.number')
                    user=$(echo "$pr" | jq -r '.user')
                    minutes_waiting=$(echo "$pr" | jq -r '.minutes_waiting')
                    MENTIONS=$(echo "$SLACK_DEV_USER_IDS" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | paste -sd' ' -)
                    payload=$(jq -n \
                      --arg channel "#review-pull-requests" \
                      --arg text ":alarm_clock: PR #$number has been waiting for $minutes_waiting minutes" \
                      --arg pr_link "<$url|$title>" \
                      --arg user "@$user" \
                      --arg time "$minutes_waiting minutes" \
                      '{
                        channel: $channel,
                        text: $text,
                        blocks: [
                          {
                            type: "header",
                            text: {
                              type: "plain_text",
                              text: "⏱️ Pull Request Waiting for Review",
                              emoji: true
                            }
                          },
                          {
                            "type": "section",
                            "fields": [
                               {
                                type: "mrkdwn",
                                text: "*PR:*\($pr_link)"
                              }
                              ]
                          },
                          {
                            type: "section",
                            fields: [
                              {
                                type: "mrkdwn",
                                text: "*Author:*\($user)"
                              },
                              {
                                type: "mrkdwn",
                                text: "*Waiting time:*\($time)"
                              }
                            ]
                          },
                          {
                            type: "context",
                            elements: [
                              {
                                type: "mrkdwn",
                                text: "⚠️ Please review soon!\ncc: $MENTIONS"
                              }
                            ]
                          }
                        ]
                      }'
                    )

                    curl -X POST https://slack.com/api/chat.postMessage \
                      -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
                      -H "Content-type: application/json" \
                      --data "$payload"
                  done
