name: 🚀 Server Deployment

on:
    push:
        tags:
            - "v*.*.*"
            - "v*.*.*-*"
    workflow_dispatch:

jobs:
    deploy:
        runs-on: self-hosted
        timeout-minutes: 60
        outputs:
            staff_api: ${{ steps.set-flags.outputs.staff_api }}
            rag_pipeline: ${{ steps.set-flags.outputs.rag_pipeline }}

        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 2

            - name: 🔖 Get Git tag
              id: tag
              run: echo "TAG=${GITHUB_REF_NAME}" >> $GITHUB_OUTPUT

            - name: 🔧 Debug tag
              run: echo "Tag is ${{ steps.tag.outputs.TAG }}"
            - name: 🔁 Determine base ref
              id: base-ref
              run: |
                  echo "BASE=$(git merge-base HEAD origin/${{ github.event.repository.default_branch }})" >> $GITHUB_OUTPUT

            - name: Run paths-filter
              id: filter
              uses: dorny/paths-filter@v2
              with:
                  base: ${{ steps.base-ref.outputs.BASE }}
                  filters: |
                      staff_api:
                        - "onexbots/staff_api/**"
                      rag_pipeline:
                        - "onexbots/rag_pipeline/**"
                      shared:
                        - "onexbots/shared/**"

            - name: 🐛 Debug filter outputs (optional)
              run: |
                  echo "shared: ${{ steps.filter.outputs.shared }}"
                  echo "staff_api: ${{ steps.filter.outputs.staff_api }}"
                  echo "rag_pipeline: ${{ steps.filter.outputs.rag_pipeline }}"

            - name: Set deployment flags
              id: set-flags
              run: |
                  if [[ "${{ steps.filter.outputs.shared }}" == 'true' ]]; then
                    echo "staff_api=true" >> $GITHUB_OUTPUT
                    echo "rag_pipeline=true" >> $GITHUB_OUTPUT
                  else
                    echo "staff_api=${{ steps.filter.outputs.staff_api }}" >> $GITHUB_OUTPUT
                    echo "rag_pipeline=${{ steps.filter.outputs.rag_pipeline }}" >> $GITHUB_OUTPUT
                  fi

    deploy-staff_api:
        needs: deploy
        if: needs.deploy.outputs.staff_api == 'true'
        uses: ./.github/workflows/deployment-staff-api.yml
        with:
            service: staff_api
            SLACK_DEV_USER_IDS: ${{ vars.SLACK_DEV_USER_IDS }}
        secrets:
            AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            AWS_REGION: ${{ secrets.AWS_REGION }}
            SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
            HOST: ${{ secrets.HOST }}
            USERNAME: ${{ secrets.USERNAME }}
            PASSWORD: ${{ secrets.PASSWORD }}
            AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}

    deploy-rag_pipeline:
        needs: deploy
        if: needs.deploy.outputs.rag_pipeline == 'true'
        uses: ./.github/workflows/deployment-rag-pipeline.yml
        with:
            service: rag_pipeline
            SLACK_DEV_USER_IDS: ${{ vars.SLACK_DEV_USER_IDS }}
        secrets:
            AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            AWS_REGION: ${{ secrets.AWS_REGION }}
            SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
            HOST: ${{ secrets.HOST }}
            USERNAME: ${{ secrets.USERNAME }}
            PASSWORD: ${{ secrets.PASSWORD }}
            AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
